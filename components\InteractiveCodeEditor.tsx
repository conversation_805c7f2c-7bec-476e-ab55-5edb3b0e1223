import React, { useState } from 'react';
import { StyleSheet, TextInput, ScrollView } from 'react-native';

import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type InteractiveCodeEditorProps = {
  code: string;
  onChangeCode: (code: string) => void;
};

export function InteractiveCodeEditor({ code, onChangeCode }: InteractiveCodeEditorProps) {
  const colorScheme = useColorScheme() ?? 'light';

  return (
    <ThemedView style={[
      styles.container,
      { backgroundColor: colorScheme === 'dark' ? '#1e1e1e' : '#f8f8f8' }
    ]}>
      <ScrollView style={styles.scrollView}>
        <TextInput
          style={[
            styles.codeInput,
            { color: colorScheme === 'dark' ? '#e6e6e6' : '#333' }
          ]}
          value={code}
          onChangeText={onChangeCode}
          multiline
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
        />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  scrollView: {
    flex: 1,
  },
  codeInput: {
    fontFamily: 'SpaceMono',
    fontSize: 14,
    padding: 12,
    minHeight: 200,
  },
});
