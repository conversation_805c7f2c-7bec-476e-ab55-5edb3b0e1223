import React, { useState } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';
import Animated, { FadeInUp } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { practiceTasks, getPracticeTasksByDifficulty } from '@/constants/PracticeData';
import { PracticeCard } from '@/components/PracticeCard';

type DifficultyFilter = 'all' | 'easy' | 'medium' | 'hard';

export default function PracticeScreen() {
  const [filter, setFilter] = useState<DifficultyFilter>('all');
  const colorScheme = useColorScheme() ?? 'light';

  const filteredTasks = () => {
    switch (filter) {
      case 'easy':
        return getPracticeTasksByDifficulty('easy');
      case 'medium':
        return getPracticeTasksByDifficulty('medium');
      case 'hard':
        return getPracticeTasksByDifficulty('hard');
      default:
        return practiceTasks;
    }
  };

  const handleTaskPress = (taskId: string) => {
    router.push(`/practice/${taskId}`);
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Uygulama Görevleri</ThemedText>
        <ThemedText>Öğrendiklerinizi pekiştirmek için pratik yapın</ThemedText>
      </ThemedView>

      <ThemedView style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && { backgroundColor: Colors[colorScheme].tint },
          ]}
          onPress={() => setFilter('all')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'all' && { color: 'white' },
            ]}
          >
            Tümü
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'easy' && { backgroundColor: Colors[colorScheme].success },
          ]}
          onPress={() => setFilter('easy')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'easy' && { color: 'white' },
            ]}
          >
            Kolay
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'medium' && { backgroundColor: Colors[colorScheme].warning },
          ]}
          onPress={() => setFilter('medium')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'medium' && { color: 'white' },
            ]}
          >
            Orta
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'hard' && { backgroundColor: Colors[colorScheme].error },
          ]}
          onPress={() => setFilter('hard')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'hard' && { color: 'white' },
            ]}
          >
            Zor
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <FlatList
        data={filteredTasks()}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        renderItem={({ item, index }) => (
          <Animated.View
            entering={FadeInUp.delay(index * 100).duration(500)}
          >
            <PracticeCard
              task={item}
              onPress={() => handleTaskPress(item.id)}
            />
          </Animated.View>
        )}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginTop: 60,
    marginBottom: 20,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterText: {
    fontSize: 14,
  },
  listContainer: {
    paddingBottom: 20,
  },
});
