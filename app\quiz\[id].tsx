import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View, ScrollView } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import Animated, { FadeIn, FadeInDown, FadeInUp } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getQuizById } from '@/constants/QuizData';

export default function QuizScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme() ?? 'light';
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [score, setScore] = useState(0);
  const [quizCompleted, setQuizCompleted] = useState(false);

  const quiz = getQuizById(id || '');

  if (!quiz) {
    return (
      <>
        <Stack.Screen options={{ title: 'Quiz Bulunamadı' }} />
        <ThemedView style={styles.container}>
          <ThemedText>Quiz bulunamadı.</ThemedText>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: Colors[colorScheme].tint }]}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.buttonText}>Geri Dön</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </>
    );
  }

  const currentQuestion = quiz.questions[currentQuestionIndex];

  const handleOptionSelect = (optionIndex: number) => {
    if (selectedOption !== null || showExplanation) return;

    setSelectedOption(optionIndex);

    if (optionIndex === currentQuestion.correctAnswer) {
      setScore(score + 1);
    }

    setShowExplanation(true);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedOption(null);
      setShowExplanation(false);
    } else {
      setQuizCompleted(true);
    }
  };

  const handleRestartQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedOption(null);
    setShowExplanation(false);
    setScore(0);
    setQuizCompleted(false);
  };

  const handleFinishQuiz = () => {
    // Gerçek uygulamada burada quiz sonuçları kaydedilir
    router.back();
  };

  if (quizCompleted) {
    const percentage = Math.round((score / quiz.questions.length) * 100);
    const isPassed = percentage >= 70; // %70 ve üzeri başarılı sayılır

    return (
      <>
        <Stack.Screen options={{ title: 'Quiz Sonuçları' }} />
        <ThemedView style={styles.container}>
          <Animated.View
            entering={FadeInDown.duration(1000)}
            style={styles.resultContainer}
          >
            <View style={[
              styles.resultIconContainer,
              { backgroundColor: isPassed ? Colors[colorScheme].success : Colors[colorScheme].error }
            ]}>
              <IconSymbol
                name={isPassed ? 'checkmark.circle.fill' : 'xmark.circle.fill'}
                size={60}
                color="white"
              />
            </View>

            <ThemedText type="title" style={styles.resultTitle}>
              {isPassed ? 'Tebrikler!' : 'Tekrar Deneyin'}
            </ThemedText>

            <ThemedText style={styles.resultScore}>
              Skorunuz: {score}/{quiz.questions.length} ({percentage}%)
            </ThemedText>

            <ThemedText style={styles.resultMessage}>
              {isPassed
                ? 'Quiz\'i başarıyla tamamladınız. Harika iş çıkardınız!'
                : 'Quiz\'i geçmek için biraz daha çalışmanız gerekiyor. Konuyu tekrar gözden geçirin ve tekrar deneyin.'}
            </ThemedText>

            <View style={styles.resultButtonsContainer}>
              <TouchableOpacity
                style={[styles.resultButton, { backgroundColor: Colors[colorScheme].tint }]}
                onPress={handleRestartQuiz}
              >
                <IconSymbol
                  name="arrow.clockwise"
                  size={20}
                  color="white"
                />
                <ThemedText style={styles.buttonText}>Tekrar Dene</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.resultButton, { backgroundColor: isPassed ? Colors[colorScheme].success : Colors[colorScheme].tint }]}
                onPress={handleFinishQuiz}
              >
                <IconSymbol
                  name={isPassed ? 'checkmark.circle.fill' : 'arrow.left'}
                  size={20}
                  color="white"
                />
                <ThemedText style={styles.buttonText}>
                  {isPassed ? 'Tamamla' : 'Geri Dön'}
                </ThemedText>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ThemedView>
      </>
    );
  }

  return (
    <>
      <Stack.Screen options={{
        title: quiz.title,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerBackButton}>
            <IconSymbol
              name="arrow.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        )
      }} />
      <ThemedView style={styles.container}>
        <ScrollView style={styles.scrollView}>
          {/* İlerleme */}
          <ThemedView style={styles.progressContainer}>
            <ThemedText style={styles.progressText}>
              Soru {currentQuestionIndex + 1}/{quiz.questions.length}
            </ThemedText>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${((currentQuestionIndex + 1) / quiz.questions.length) * 100}%`,
                    backgroundColor: Colors[colorScheme].tint
                  }
                ]}
              />
            </View>
          </ThemedView>

          {/* Soru */}
          <Animated.View
            entering={FadeIn.duration(500)}
            style={styles.questionContainer}
          >
            <ThemedText type="defaultSemiBold" style={styles.question}>
              {currentQuestion.question}
            </ThemedText>
          </Animated.View>

          {/* Seçenekler */}
          <ThemedView style={styles.optionsContainer}>
            {currentQuestion.options.map((option, index) => (
              <Animated.View
                key={index}
                entering={FadeInUp.delay(index * 100).duration(500)}
              >
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    selectedOption === index && {
                      backgroundColor: index === currentQuestion.correctAnswer
                        ? Colors[colorScheme].success
                        : Colors[colorScheme].error,
                      borderColor: index === currentQuestion.correctAnswer
                        ? Colors[colorScheme].success
                        : Colors[colorScheme].error,
                    },
                    selectedOption !== null && index === currentQuestion.correctAnswer && {
                      backgroundColor: Colors[colorScheme].success,
                      borderColor: Colors[colorScheme].success,
                    }
                  ]}
                  onPress={() => handleOptionSelect(index)}
                  disabled={selectedOption !== null}
                >
                  <ThemedText style={[
                    styles.optionText,
                    (selectedOption === index ||
                     (selectedOption !== null && index === currentQuestion.correctAnswer)) &&
                    { color: 'white' }
                  ]}>
                    {String.fromCharCode(65 + index)}. {option}
                  </ThemedText>

                  {selectedOption !== null && index === currentQuestion.correctAnswer && (
                    <IconSymbol
                      name="checkmark.circle.fill"
                      size={20}
                      color="white"
                    />
                  )}

                  {selectedOption === index && index !== currentQuestion.correctAnswer && (
                    <IconSymbol
                      name="xmark.circle.fill"
                      size={20}
                      color="white"
                    />
                  )}
                </TouchableOpacity>
              </Animated.View>
            ))}
          </ThemedView>

          {/* Açıklama */}
          {showExplanation && (
            <Animated.View
              entering={FadeInDown.duration(500)}
              style={[
                styles.explanationContainer,
                {
                  backgroundColor: selectedOption === currentQuestion.correctAnswer
                    ? 'rgba(76, 175, 80, 0.1)'
                    : 'rgba(244, 67, 54, 0.1)'
                }
              ]}
            >
              <ThemedText type="defaultSemiBold" style={styles.explanationTitle}>
                {selectedOption === currentQuestion.correctAnswer ? 'Doğru!' : 'Yanlış!'}
              </ThemedText>
              <ThemedText style={styles.explanation}>
                {currentQuestion.explanation}
              </ThemedText>
            </Animated.View>
          )}

          {/* İleri Butonu */}
          {showExplanation && (
            <TouchableOpacity
              style={[styles.nextButton, { backgroundColor: Colors[colorScheme].tint }]}
              onPress={handleNextQuestion}
            >
              <ThemedText style={styles.buttonText}>
                {currentQuestionIndex < quiz.questions.length - 1 ? 'Sonraki Soru' : 'Sonuçları Gör'}
              </ThemedText>
              <IconSymbol
                name="arrow.right"
                size={20}
                color="white"
              />
            </TouchableOpacity>
          )}
        </ScrollView>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  progressContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  progressText: {
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  questionContainer: {
    padding: 20,
  },
  question: {
    fontSize: 18,
    lineHeight: 26,
  },
  optionsContainer: {
    padding: 20,
    paddingTop: 0,
  },
  optionButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    marginBottom: 12,
  },
  optionText: {
    flex: 1,
  },
  explanationContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 8,
  },
  explanationTitle: {
    marginBottom: 8,
  },
  explanation: {
    lineHeight: 22,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    margin: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  button: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    margin: 20,
  },
  resultContainer: {
    padding: 20,
    alignItems: 'center',
  },
  resultIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  resultTitle: {
    marginBottom: 8,
  },
  resultScore: {
    fontSize: 18,
    marginBottom: 16,
  },
  resultMessage: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  resultButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  resultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    flex: 0.45,
  },
  headerBackButton: {
    padding: 8,
    marginLeft: 8,
  },
});
