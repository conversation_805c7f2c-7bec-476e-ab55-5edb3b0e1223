import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { Link, Stack, router } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';

import { IconSymbol } from '@/components/ui/IconSymbol';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function RegisterScreen() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const colorScheme = useColorScheme() ?? 'light';

  const handleRegister = () => {
    // Basit doğrulama
    if (!username || !email || !password || !confirmPassword) {
      setError('Lütfen tüm alanları doldurun');
      return;
    }

    if (password !== confirmPassword) {
      setError('Şifreler eşleşmiyor');
      return;
    }

    // Gerçek uygulamada burada API çağrısı yapılır
    // Şimdilik seviye seçim ekranına yönlendirelim
    router.replace('/level-select');
  };

  return (
    <>
      <Stack.Screen options={{
        title: 'Kayıt Ol',
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerBackButton}>
            <IconSymbol
              name="arrow.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        )
      }} />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <ThemedView style={styles.container}>
          <Animated.View entering={FadeIn.duration(1000)} style={styles.formContainer}>
            <ThemedText type="title" style={styles.title}>
              Hesap Oluştur
            </ThemedText>

            <ThemedText style={styles.label}>Kullanıcı Adı</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  borderColor: Colors[colorScheme].border,
                  color: Colors[colorScheme].text
                }
              ]}
              placeholder="Kullanıcı adınızı girin"
              placeholderTextColor={Colors[colorScheme].icon}
              value={username}
              onChangeText={setUsername}
            />

            <ThemedText style={styles.label}>E-posta</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  borderColor: Colors[colorScheme].border,
                  color: Colors[colorScheme].text
                }
              ]}
              placeholder="E-posta adresinizi girin"
              placeholderTextColor={Colors[colorScheme].icon}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <ThemedText style={styles.label}>Şifre</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  borderColor: Colors[colorScheme].border,
                  color: Colors[colorScheme].text
                }
              ]}
              placeholder="Şifrenizi girin"
              placeholderTextColor={Colors[colorScheme].icon}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />

            <ThemedText style={styles.label}>Şifre Tekrar</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  borderColor: Colors[colorScheme].border,
                  color: Colors[colorScheme].text
                }
              ]}
              placeholder="Şifrenizi tekrar girin"
              placeholderTextColor={Colors[colorScheme].icon}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
            />

            {error ? (
              <ThemedText style={{ color: Colors[colorScheme].error, marginTop: 10 }}>
                {error}
              </ThemedText>
            ) : null}

            <TouchableOpacity
              style={[styles.registerButton, { backgroundColor: Colors[colorScheme].tint }]}
              onPress={handleRegister}
            >
              <ThemedText style={styles.buttonText}>Kayıt Ol</ThemedText>
            </TouchableOpacity>

            <ThemedView style={styles.loginContainer}>
              <ThemedText>Zaten bir hesabınız var mı?</ThemedText>
              <Link href="/auth/login" style={styles.loginLink}>
                <ThemedText type="link">Giriş Yap</ThemedText>
              </Link>
            </ThemedView>
          </Animated.View>
        </ThemedView>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    marginTop: 20,
  },
  title: {
    textAlign: 'center',
    marginBottom: 30,
  },
  label: {
    marginBottom: 5,
    marginTop: 15,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  registerButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 30,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    gap: 5,
  },
  loginLink: {
    marginLeft: 5,
  },
  headerBackButton: {
    padding: 8,
    marginLeft: 8,
  },
});
