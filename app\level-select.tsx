import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Stack, router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { LevelSelector } from '@/components/LevelSelector';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function LevelSelectScreen() {
  const [selectedLevel, setSelectedLevel] = useState<string | null>(null);
  const colorScheme = useColorScheme() ?? 'light';

  const handleContinue = () => {
    if (selectedLevel) {
      // Seviye bilgisini saklayıp ana ekrana yönlendir
      router.replace('/(tabs)');
    }
  };

  return (
    <>
      <Stack.Screen options={{
        title: "Seviye Seçimi",
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerBackButton}>
            <IconSymbol
              name="arrow.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        )
      }} />
      <ThemedView style={styles.container}>
        <Animated.View entering={FadeInDown.duration(1000)} style={styles.contentContainer}>
        <ThemedText type="title" style={styles.title}>
          Seviyenizi Seçin
        </ThemedText>

        <ThemedText style={styles.subtitle}>
          Size en uygun eğitim içeriğini sunabilmemiz için seviyenizi seçin.
        </ThemedText>

        <ThemedView style={styles.levelContainer}>
          <LevelSelector
            onSelectLevel={setSelectedLevel}
            selectedLevel={selectedLevel}
          />
        </ThemedView>

        <TouchableOpacity
          style={[
            styles.continueButton,
            {
              backgroundColor: Colors[colorScheme].tint,
              opacity: selectedLevel ? 1 : 0.5
            }
          ]}
          onPress={handleContinue}
          disabled={!selectedLevel}
        >
          <ThemedText style={styles.buttonText}>Başla</ThemedText>
        </TouchableOpacity>
      </Animated.View>
    </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },
  title: {
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 20,
  },
  levelContainer: {
    width: '100%',
    marginVertical: 20,
  },
  continueButton: {
    width: '100%',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  headerBackButton: {
    padding: 8,
    marginLeft: 8,
  },
});
