import React, { useState } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';
import Animated, { FadeInUp } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { allModules, beginnerModules, intermediateModules, advancedModules } from '@/constants/ModuleData';
import { ModuleCard } from '@/components/ModuleCard';

type LevelFilter = 'all' | 'beginner' | 'intermediate' | 'advanced';

export default function ModulesScreen() {
  const [filter, setFilter] = useState<LevelFilter>('all');
  const colorScheme = useColorScheme() ?? 'light';

  const filteredModules = () => {
    switch (filter) {
      case 'beginner':
        return beginnerModules;
      case 'intermediate':
        return intermediateModules;
      case 'advanced':
        return advancedModules;
      default:
        return allModules;
    }
  };

  const handleModulePress = (moduleId: string) => {
    router.push(`/module/${moduleId}`);
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Eğitim Modülleri</ThemedText>
        <ThemedText>React Native öğrenmek için adım adım modüller</ThemedText>
      </ThemedView>

      <ThemedView style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'all' && { backgroundColor: Colors[colorScheme].tint },
          ]}
          onPress={() => setFilter('all')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'all' && { color: 'white' },
            ]}
          >
            Tümü
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'beginner' && { backgroundColor: Colors[colorScheme].tint },
          ]}
          onPress={() => setFilter('beginner')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'beginner' && { color: 'white' },
            ]}
          >
            Başlangıç
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'intermediate' && { backgroundColor: Colors[colorScheme].tint },
          ]}
          onPress={() => setFilter('intermediate')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'intermediate' && { color: 'white' },
            ]}
          >
            Orta
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            filter === 'advanced' && { backgroundColor: Colors[colorScheme].tint },
          ]}
          onPress={() => setFilter('advanced')}
        >
          <ThemedText
            style={[
              styles.filterText,
              filter === 'advanced' && { color: 'white' },
            ]}
          >
            İleri
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <FlatList
        data={filteredModules()}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        renderItem={({ item, index }) => (
          <Animated.View
            entering={FadeInUp.delay(index * 100).duration(500)}
          >
            <ModuleCard
              module={item}
              onPress={() => handleModulePress(item.id)}
            />
          </Animated.View>
        )}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginTop: 60,
    marginBottom: 20,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterText: {
    fontSize: 14,
  },
  listContainer: {
    paddingBottom: 20,
  },
});
