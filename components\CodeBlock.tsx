import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, ScrollView, View } from 'react-native';
import * as Clipboard from 'expo-clipboard';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type CodeBlockProps = {
  code: string;
};

export function CodeBlock({ code }: CodeBlockProps) {
  const colorScheme = useColorScheme() ?? 'light';
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await Clipboard.setStringAsync(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={[
        styles.codeHeader,
        { backgroundColor: colorScheme === 'dark' ? '#2d2d2d' : '#f5f5f5' }
      ]}>
        <ThemedText style={styles.language}>JavaScript</ThemedText>
        <TouchableOpacity onPress={handleCopy} style={styles.copyButton}>
          <IconSymbol
            name={copied ? 'checkmark.circle.fill' : 'doc.text.fill'}
            size={20}
            color={copied ? Colors[colorScheme].success : Colors[colorScheme].icon}
          />
          <ThemedText style={[
            styles.copyText,
            copied && { color: Colors[colorScheme].success }
          ]}>
            {copied ? 'Kopyalandı' : 'Kopyala'}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
      
      <ScrollView 
        horizontal 
        style={styles.codeScrollView}
        showsHorizontalScrollIndicator={false}
      >
        <ScrollView 
          style={[
            styles.codeContainer,
            { backgroundColor: colorScheme === 'dark' ? '#1e1e1e' : '#f8f8f8' }
          ]}
        >
          <ThemedText style={[
            styles.codeText,
            { color: colorScheme === 'dark' ? '#e6e6e6' : '#333' }
          ]}>
            {code}
          </ThemedText>
        </ScrollView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  codeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  language: {
    fontSize: 12,
    opacity: 0.7,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyText: {
    fontSize: 12,
    marginLeft: 4,
  },
  codeScrollView: {
    maxHeight: 300,
  },
  codeContainer: {
    padding: 12,
    minWidth: '100%',
  },
  codeText: {
    fontFamily: 'SpaceMono',
    fontSize: 14,
  },
});
