import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Module } from '@/constants/ModuleData';

type ModuleCardProps = {
  module: Module;
  onPress: () => void;
};

export function ModuleCard({ module, onPress }: ModuleCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Seviyeye göre renk ve ikon belirle
  const getLevelInfo = () => {
    switch (module.level) {
      case 'beginner':
        return {
          color: '#4CAF50',
          icon: 'star.fill',
          text: 'Başlangıç'
        };
      case 'intermediate':
        return {
          color: '#FF9800',
          icon: 'star.leadinghalf.filled',
          text: 'Orta'
        };
      case 'advanced':
        return {
          color: '#F44336',
          icon: 'star.circle.fill',
          text: 'İleri'
        };
      default:
        return {
          color: '#2196F3',
          icon: 'star.fill',
          text: 'Başlangıç'
        };
    }
  };

  const levelInfo = getLevelInfo();

  return (
    <TouchableOpacity
      style={[
        styles.card,
        { backgroundColor: Colors[colorScheme].card }
      ]}
      onPress={onPress}
    >
      <View style={styles.iconContainer}>
        <IconSymbol
          name={module.icon as any}
          size={32}
          color={Colors[colorScheme].tint}
        />
      </View>
      
      <View style={styles.contentContainer}>
        <ThemedText type="defaultSemiBold" style={styles.title}>
          {module.title}
        </ThemedText>
        
        <ThemedText style={styles.description}>
          {module.description}
        </ThemedText>
        
        <View style={styles.metaContainer}>
          <View style={styles.levelContainer}>
            <IconSymbol
              name={levelInfo.icon as any}
              size={16}
              color={levelInfo.color}
            />
            <ThemedText style={[styles.levelText, { color: levelInfo.color }]}>
              {levelInfo.text}
            </ThemedText>
          </View>
          
          <ThemedText style={styles.duration}>
            {module.duration} dakika
          </ThemedText>
        </View>
      </View>
      
      <IconSymbol
        name="arrow.right"
        size={24}
        color={Colors[colorScheme].icon}
        style={styles.arrowIcon}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(97, 218, 251, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.8,
  },
  metaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  levelText: {
    fontSize: 12,
    marginLeft: 4,
  },
  duration: {
    fontSize: 12,
    opacity: 0.6,
  },
  arrowIcon: {
    marginLeft: 8,
  },
});
