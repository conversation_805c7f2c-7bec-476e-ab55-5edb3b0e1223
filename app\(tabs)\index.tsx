import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View, Image } from 'react-native';
import { router } from 'expo-router';
import Animated, { FadeIn, FadeInRight } from 'react-native-reanimated';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { beginnerModules } from '@/constants/ModuleData';
import { practiceTasks } from '@/constants/PracticeData';
import { ProgressBar } from '@/components/ProgressBar';

export default function LearnScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [progress, setProgress] = useState(0);

  // Örnek ilerleme durumu
  useEffect(() => {
    // Gerçek uygulamada bu değer kullanıcının ilerlemesine göre hesaplanır
    setProgress(0.25); // %25 ilerleme
  }, []);

  const handleContinueLearning = () => {
    // Kullanıcının kaldığı yerden devam etmesi için
    // Gerçek uygulamada bu, kullanıcının son çalıştığı modüle yönlendirir
    router.push(`/module/${beginnerModules[0].id}`);
  };

  const handleModulePress = (moduleId: string) => {
    router.push(`/module/${moduleId}`);
  };

  const handlePracticePress = (taskId: string) => {
    router.push(`/practice/${taskId}`);
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">React Native Öğren</ThemedText>
        <HelloWave />
      </ThemedView>

      <ThemedView style={styles.progressContainer}>
        <ThemedView style={styles.progressLabelContainer}>
          <ThemedText type="defaultSemiBold">Genel İlerleme</ThemedText>
          <ThemedText>{Math.round(progress * 100)}%</ThemedText>
        </ThemedView>
        <ProgressBar progress={progress} />
      </ThemedView>

      <TouchableOpacity
        style={[styles.continueButton, { backgroundColor: Colors[colorScheme].tint }]}
        onPress={handleContinueLearning}
      >
        <ThemedText style={styles.buttonText}>Öğrenmeye Devam Et</ThemedText>
        <IconSymbol name="arrow.right" size={20} color="white" />
      </TouchableOpacity>

      {/* Önerilen Modüller */}
      <ThemedView style={styles.section}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Önerilen Modüller</ThemedText>
          <TouchableOpacity onPress={() => router.push('/modules')}>
            <ThemedText type="link">Tümünü Gör</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
          {beginnerModules.slice(0, 3).map((module, index) => (
            <Animated.View
              key={module.id}
              entering={FadeInRight.delay(index * 100).duration(500)}
            >
              <TouchableOpacity
                style={[
                  styles.moduleCard,
                  { backgroundColor: Colors[colorScheme].moduleCard }
                ]}
                onPress={() => handleModulePress(module.id)}
              >
                <View style={styles.moduleIconContainer}>
                  <IconSymbol
                    name={module.icon as any}
                    size={24}
                    color={Colors[colorScheme].text}
                  />
                </View>
                <ThemedText type="defaultSemiBold" style={styles.moduleTitle}>
                  {module.title}
                </ThemedText>
                <ThemedText style={styles.moduleDuration}>
                  {module.duration} dakika
                </ThemedText>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </ScrollView>
      </ThemedView>

      {/* Uygulama Görevleri */}
      <ThemedView style={styles.section}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Uygulama Görevleri</ThemedText>
          <TouchableOpacity onPress={() => router.push('/practice')}>
            <ThemedText type="link">Tümünü Gör</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
          {practiceTasks.slice(0, 3).map((task, index) => (
            <Animated.View
              key={task.id}
              entering={FadeInRight.delay(index * 100).duration(500)}
            >
              <TouchableOpacity
                style={[
                  styles.practiceCard,
                  { backgroundColor: Colors[colorScheme].practiceCard }
                ]}
                onPress={() => handlePracticePress(task.id)}
              >
                <ThemedText type="defaultSemiBold" style={styles.practiceTitle}>
                  {task.title}
                </ThemedText>
                <ThemedText style={styles.practiceDescription} numberOfLines={2}>
                  {task.description}
                </ThemedText>
                <View style={styles.practiceFooter}>
                  <View style={styles.difficultyContainer}>
                    <IconSymbol
                      name={task.difficulty === 'easy' ? 'star.fill' :
                            task.difficulty === 'medium' ? 'star.leadinghalf.filled' : 'star.circle.fill'}
                      size={16}
                      color={task.difficulty === 'easy' ? Colors[colorScheme].success :
                             task.difficulty === 'medium' ? Colors[colorScheme].warning : Colors[colorScheme].error}
                    />
                    <ThemedText style={styles.difficultyText}>
                      {task.difficulty === 'easy' ? 'Kolay' :
                       task.difficulty === 'medium' ? 'Orta' : 'Zor'}
                    </ThemedText>
                  </View>
                  <ThemedText style={styles.practiceDuration}>
                    {task.duration} dk
                  </ThemedText>
                </View>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </ScrollView>
      </ThemedView>

      {/* İpucu Kartı */}
      <Animated.View entering={FadeIn.delay(500).duration(1000)}>
        <ThemedView style={[styles.tipCard, { backgroundColor: Colors[colorScheme].info }]}>
          <View style={styles.tipIconContainer}>
            <IconSymbol name="questionmark.circle.fill" size={32} color="white" />
          </View>
          <View style={styles.tipContent}>
            <ThemedText style={styles.tipTitle}>Günün İpucu</ThemedText>
            <ThemedText style={styles.tipText}>
              React Native'de stil özellikleri CSS'e benzer ancak camelCase kullanır. Örneğin, background-color yerine backgroundColor kullanılır.
            </ThemedText>
          </View>
        </ThemedView>
      </Animated.View>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 20,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginRight: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  horizontalScroll: {
    paddingBottom: 8,
  },
  moduleCard: {
    width: 150,
    height: 150,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    justifyContent: 'space-between',
  },
  moduleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moduleTitle: {
    fontSize: 16,
  },
  moduleDuration: {
    fontSize: 12,
    opacity: 0.7,
  },
  practiceCard: {
    width: 200,
    height: 150,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    justifyContent: 'space-between',
  },
  practiceTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  practiceDescription: {
    fontSize: 12,
    opacity: 0.8,
    flex: 1,
  },
  practiceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyText: {
    fontSize: 12,
    marginLeft: 4,
  },
  practiceDuration: {
    fontSize: 12,
    opacity: 0.7,
  },
  tipCard: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  tipIconContainer: {
    marginRight: 16,
  },
  tipContent: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  tipText: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
  },
});
