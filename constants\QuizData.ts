// React Native öğrenme uygulaması için quiz verileri
export type QuizQuestion = {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number; // Do<PERSON>ru cevabın options dizisindeki indeksi
  explanation: string;
};

export type Quiz = {
  id: string;
  lessonId: string;
  title: string;
  description: string;
  questions: QuizQuestion[];
};

export const quizzes: Quiz[] = [
  {
    id: 'quiz-intro-to-react-native',
    lessonId: 'what-is-react-native',
    title: 'React Native\'e Giriş Quiz',
    description: 'React Native temelleri hakkında bilginizi test edin',
    questions: [
      {
        id: 'q1',
        question: 'React Native hangi şirket tarafından geliştirilmiştir?',
        options: ['Google', 'Apple', 'Facebook', 'Microsoft'],
        correctAnswer: 2,
        explanation: 'React Native, Facebook tarafından geliştirilmiş açık kaynaklı bir mobil uygulama geliştirme framework\'üdür.'
      },
      {
        id: 'q2',
        question: 'React Native ile hangi platformlar için uygulama geliştirebilirsiniz?',
        options: ['Sadece iOS', 'Sadece Android', 'iOS ve Android', 'iOS, Android ve Windows Phone'],
        correctAnswer: 2,
        explanation: 'React Native ile iOS ve Android platformları için native mobil uygulamalar geliştirebilirsiniz.'
      },
      {
        id: 'q3',
        question: 'React Native\'in en büyük avantajlarından biri nedir?',
        options: [
          'Sadece Swift dilini kullanması', 
          'Tek kod tabanı ile birden fazla platform için geliştirme yapılabilmesi', 
          'Native uygulamalardan daha hızlı olması', 
          'Sadece web geliştiricileri tarafından kullanılabilmesi'
        ],
        correctAnswer: 1,
        explanation: 'React Native\'in en büyük avantajlarından biri, tek bir kod tabanı kullanarak hem iOS hem de Android için uygulama geliştirebilmenizdir.'
      },
      {
        id: 'q4',
        question: 'React Native uygulamaları hangi programlama dilini kullanır?',
        options: ['Swift', 'Java', 'C#', 'JavaScript'],
        correctAnswer: 3,
        explanation: 'React Native uygulamaları JavaScript programlama dilini kullanır.'
      },
      {
        id: 'q5',
        question: 'React Native\'de "Hot Reloading" ne işe yarar?',
        options: [
          'Uygulamanın performansını artırır', 
          'Uygulamanın boyutunu küçültür', 
          'Kod değişikliklerini anında görebilmenizi sağlar', 
          'Uygulamanın güvenliğini artırır'
        ],
        correctAnswer: 2,
        explanation: 'Hot Reloading, kod değişikliklerini anında görebilmenizi sağlayan bir özelliktir. Bu sayede uygulamayı yeniden başlatmadan değişiklikleri test edebilirsiniz.'
      }
    ]
  },
  {
    id: 'quiz-jsx-basics',
    lessonId: 'understanding-jsx',
    title: 'JSX Temelleri Quiz',
    description: 'JSX sözdizimi hakkında bilginizi test edin',
    questions: [
      {
        id: 'q1',
        question: 'JSX nedir?',
        options: [
          'Java Syntax Extension', 
          'JavaScript XML', 
          'JavaScript Extra', 
          'Java Script Extension'
        ],
        correctAnswer: 1,
        explanation: 'JSX, JavaScript XML\'in kısaltmasıdır. JavaScript içinde HTML benzeri kod yazmanıza olanak tanıyan bir sözdizimi uzantısıdır.'
      },
      {
        id: 'q2',
        question: 'JSX içinde JavaScript ifadeleri nasıl kullanılır?',
        options: [
          'Köşeli parantezler içinde: [expression]', 
          'Süslü parantezler içinde: {expression}', 
          'Yıldız işaretleri içinde: *expression*', 
          'Dolar işareti ile: $expression'
        ],
        correctAnswer: 1,
        explanation: 'JSX içinde JavaScript ifadeleri süslü parantezler {} içinde kullanılır.'
      },
      {
        id: 'q3',
        question: 'Aşağıdakilerden hangisi geçerli bir JSX ifadesidir?',
        options: [
          '<Text>Merhaba</Text><Text>Dünya</Text>', 
          '<View><Text>Merhaba Dünya</Text></View>', 
          '<text>Merhaba Dünya</text>', 
          '<View>Merhaba Dünya</View>'
        ],
        correctAnswer: 1,
        explanation: 'Her JSX ifadesi tek bir kök element içermelidir. İkinci seçenek doğrudur çünkü <View> kök elementi içinde <Text> elementi bulunmaktadır.'
      },
      {
        id: 'q4',
        question: 'JSX\'te stil özellikleri nasıl tanımlanır?',
        options: [
          'style="color: red"', 
          'style={color: "red"}', 
          'style={{color: "red"}}', 
          'style=[color: "red"]'
        ],
        correctAnswer: 2,
        explanation: 'JSX\'te stil özellikleri çift süslü parantez içinde JavaScript nesnesi olarak tanımlanır: style={{color: "red"}}'
      },
      {
        id: 'q5',
        question: 'JSX\'te HTML\'den farklı olarak hangi özellik adlandırma kuralı kullanılır?',
        options: [
          'Tüm özellikler büyük harfle başlar', 
          'Tüm özellikler küçük harfle yazılır', 
          'camelCase kullanılır (örn. backgroundColor)', 
          'snake_case kullanılır (örn. background_color)'
        ],
        correctAnswer: 2,
        explanation: 'JSX\'te özellikler camelCase olarak yazılır. Örneğin, HTML\'deki background-color yerine backgroundColor kullanılır.'
      }
    ]
  },
  {
    id: 'quiz-components-and-props',
    lessonId: 'functional-components',
    title: 'Bileşenler ve Props Quiz',
    description: 'React Native bileşenleri ve props kavramı hakkında bilginizi test edin',
    questions: [
      {
        id: 'q1',
        question: 'React Native\'de bir fonksiyonel bileşen nasıl tanımlanır?',
        options: [
          'function MyComponent() { return <View><Text>Hello</Text></View>; }', 
          'class MyComponent { render() { return <View><Text>Hello</Text></View>; } }', 
          'const MyComponent = <View><Text>Hello</Text></View>;', 
          'var MyComponent = function() { <View><Text>Hello</Text></View>; }'
        ],
        correctAnswer: 0,
        explanation: 'Fonksiyonel bileşenler, JSX döndüren JavaScript fonksiyonlarıdır. İlk seçenek doğru bir fonksiyonel bileşen tanımıdır.'
      },
      {
        id: 'q2',
        question: 'Props nedir?',
        options: [
          'Bileşenin iç durumunu yöneten özellikler', 
          'Bileşene veri aktarmak için kullanılan özellikler', 
          'Bileşenin stilini tanımlayan özellikler', 
          'Bileşenin yaşam döngüsünü kontrol eden özellikler'
        ],
        correctAnswer: 1,
        explanation: 'Props (properties), bileşenlere veri aktarmak için kullanılan özelliklerdir. Bir bileşeni çağırırken özellikler ekleyebilir ve bileşen içinde bu özelliklere erişebilirsiniz.'
      },
      {
        id: 'q3',
        question: 'Aşağıdakilerden hangisi bir bileşene props aktarmanın doğru yoludur?',
        options: [
          '<MyComponent props={name: "John"}>', 
          '<MyComponent name="John">', 
          '<MyComponent name=John>', 
          '<MyComponent>name="John"</MyComponent>'
        ],
        correctAnswer: 1,
        explanation: 'Bir bileşene props aktarmak için özellik adı ve değerini doğrudan bileşen çağrısında belirtirsiniz. İkinci seçenek doğrudur.'
      },
      {
        id: 'q4',
        question: 'Bir bileşen içinde props\'lara nasıl erişilir?',
        options: [
          'this.props.propertyName', 
          'props.propertyName', 
          'this.propertyName', 
          'propertyName'
        ],
        correctAnswer: 1,
        explanation: 'Fonksiyonel bileşenlerde, props parametresi aracılığıyla props\'lara erişirsiniz: props.propertyName'
      },
      {
        id: 'q5',
        question: 'Destructuring kullanarak props\'ları nasıl alırsınız?',
        options: [
          'function MyComponent(props) { const { name, age } = props; ... }', 
          'function MyComponent({ name, age }) { ... }', 
          'function MyComponent() { const { name, age } = this.props; ... }', 
          'function MyComponent(props[name, age]) { ... }'
        ],
        correctAnswer: 1,
        explanation: 'Modern JavaScript\'te, props\'ları daha temiz bir şekilde kullanmak için destructuring kullanabilirsiniz. İkinci seçenek doğru kullanımı göstermektedir.'
      }
    ]
  },
  {
    id: 'quiz-navigation',
    lessonId: 'stack-navigation',
    title: 'React Navigation Quiz',
    description: 'React Navigation ve ekranlar arası geçiş hakkında bilginizi test edin',
    questions: [
      {
        id: 'q1',
        question: 'React Navigation\'da Stack Navigator ne işe yarar?',
        options: [
          'Ekranlar arasında yatay geçiş sağlar', 
          'Ekranları bir yığın (stack) olarak yönetir', 
          'Alt sekmeleri yönetir', 
          'Çekmece menüsü oluşturur'
        ],
        correctAnswer: 1,
        explanation: 'Stack Navigator, ekranları bir yığın (stack) olarak yönetir. Yeni bir ekrana gittiğinizde, o ekran yığının üstüne eklenir.'
      },
      {
        id: 'q2',
        question: 'Bir ekrandan diğerine geçmek için hangi fonksiyon kullanılır?',
        options: [
          'navigation.goTo()', 
          'navigation.navigate()', 
          'navigation.push()', 
          'navigation.change()'
        ],
        correctAnswer: 1,
        explanation: 'Bir ekrandan diğerine geçmek için navigation.navigate() fonksiyonu kullanılır.'
      },
      {
        id: 'q3',
        question: 'Bir ekrandan bir önceki ekrana dönmek için hangi fonksiyon kullanılır?',
        options: [
          'navigation.back()', 
          'navigation.return()', 
          'navigation.goBack()', 
          'navigation.previous()'
        ],
        correctAnswer: 2,
        explanation: 'Bir önceki ekrana dönmek için navigation.goBack() fonksiyonu kullanılır.'
      },
      {
        id: 'q4',
        question: 'Bir ekrandan diğerine parametre göndermek için doğru yöntem nedir?',
        options: [
          'navigation.navigate("ScreenName", { paramName: value })', 
          'navigation.navigate("ScreenName") with params={paramName: value}', 
          'navigation.navigate("ScreenName") and navigation.setParams({ paramName: value })', 
          'navigation.navigate("ScreenName?paramName=value")'
        ],
        correctAnswer: 0,
        explanation: 'Bir ekrandan diğerine parametre göndermek için navigation.navigate() fonksiyonunun ikinci parametresi olarak bir nesne geçirilir.'
      },
      {
        id: 'q5',
        question: 'Bir ekranda, kendisine gönderilen parametrelere nasıl erişilir?',
        options: [
          'navigation.params', 
          'props.params', 
          'route.params', 
          'this.params'
        ],
        correctAnswer: 2,
        explanation: 'Bir ekrana gönderilen parametrelere route.params nesnesi aracılığıyla erişilir.'
      }
    ]
  }
];

// Quiz ID'sine göre quiz getir
export function getQuizById(id: string): Quiz | undefined {
  return quizzes.find(quiz => quiz.id === id);
}

// Ders ID'sine göre quiz getir
export function getQuizByLessonId(lessonId: string): Quiz | undefined {
  return quizzes.find(quiz => quiz.lessonId === lessonId);
}
