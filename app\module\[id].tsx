import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View, Modal } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getModuleById } from '@/constants/ModuleData';
import { getQuizByLessonId } from '@/constants/QuizData';
import { CodeBlock } from '@/components/CodeBlock';

export default function ModuleDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme() ?? 'light';
  const [selectedLessonIndex, setSelectedLessonIndex] = useState(0);
  const [showHintModal, setShowHintModal] = useState(false);
  const [currentHintIndex, setCurrentHintIndex] = useState(0);

  const module = getModuleById(id || '');

  if (!module) {
    return (
      <>
        <Stack.Screen options={{ title: 'Modül Bulunamadı' }} />
        <ThemedView style={styles.container}>
          <ThemedText>Modül bulunamadı.</ThemedText>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: Colors[colorScheme].tint }]}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.buttonText}>Geri Dön</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </>
    );
  }

  const selectedLesson = module.lessons[selectedLessonIndex];
  const hasQuiz = selectedLesson.hasQuiz;
  const quiz = hasQuiz ? getQuizByLessonId(selectedLesson.id) : null;
  const hasHints = selectedLesson.hints && selectedLesson.hints.length > 0;

  const handleQuizPress = () => {
    if (quiz) {
      router.push(`/quiz/${quiz.id}`);
    }
  };

  const handleShowHint = () => {
    setShowHintModal(true);
  };

  const handleNextHint = () => {
    if (selectedLesson.hints && currentHintIndex < selectedLesson.hints.length - 1) {
      setCurrentHintIndex(currentHintIndex + 1);
    } else {
      setShowHintModal(false);
      setCurrentHintIndex(0);
    }
  };

  const handleCloseHints = () => {
    setShowHintModal(false);
    setCurrentHintIndex(0);
  };

  return (
    <>
      <Stack.Screen options={{
        title: module.title,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerBackButton}>
            <IconSymbol
              name="arrow.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        )
      }} />
      <ThemedView style={styles.container}>
        <ScrollView style={styles.scrollView}>
          {/* Modül Başlığı */}
          <ThemedView style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: Colors[colorScheme].moduleCard }]}>
              <IconSymbol
                name={module.icon as any}
                size={32}
                color={Colors[colorScheme].text}
              />
            </View>
            <ThemedText type="title" style={styles.title}>
              {module.title}
            </ThemedText>
            <ThemedText style={styles.description}>
              {module.description}
            </ThemedText>

            <ThemedView style={styles.metaContainer}>
              <View style={styles.metaItem}>
                <IconSymbol
                  name="clock.fill"
                  size={16}
                  color={Colors[colorScheme].icon}
                />
                <ThemedText style={styles.metaText}>
                  {module.duration} dakika
                </ThemedText>
              </View>

              <View style={styles.metaItem}>
                <IconSymbol
                  name="book.fill"
                  size={16}
                  color={Colors[colorScheme].icon}
                />
                <ThemedText style={styles.metaText}>
                  {module.lessons.length} ders
                </ThemedText>
              </View>

              <View style={styles.metaItem}>
                <IconSymbol
                  name={module.level === 'beginner' ? 'star.fill' :
                        module.level === 'intermediate' ? 'star.leadinghalf.filled' : 'star.circle.fill'}
                  size={16}
                  color={module.level === 'beginner' ? Colors[colorScheme].success :
                         module.level === 'intermediate' ? Colors[colorScheme].warning : Colors[colorScheme].error}
                />
                <ThemedText style={styles.metaText}>
                  {module.level === 'beginner' ? 'Başlangıç' :
                   module.level === 'intermediate' ? 'Orta' : 'İleri'}
                </ThemedText>
              </View>
            </ThemedView>
          </ThemedView>

          {/* Ders Seçimi */}
          <ThemedView style={styles.lessonSelector}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {module.lessons.map((lesson, index) => (
                <TouchableOpacity
                  key={lesson.id}
                  style={[
                    styles.lessonTab,
                    selectedLessonIndex === index && {
                      backgroundColor: Colors[colorScheme].tint,
                      borderColor: Colors[colorScheme].tint,
                    }
                  ]}
                  onPress={() => setSelectedLessonIndex(index)}
                >
                  <ThemedText
                    style={[
                      styles.lessonTabText,
                      selectedLessonIndex === index && { color: 'white' }
                    ]}
                  >
                    {index + 1}. {lesson.title}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </ThemedView>

          {/* Ders İçeriği */}
          <Animated.View
            entering={FadeInDown.duration(500)}
            style={styles.lessonContent}
          >
            <View style={styles.lessonHeader}>
              <ThemedText type="subtitle" style={styles.lessonTitle}>
                {selectedLesson.title}
              </ThemedText>

              {hasHints && (
                <TouchableOpacity
                  style={styles.hintButton}
                  onPress={handleShowHint}
                >
                  <IconSymbol
                    name="questionmark.circle.fill"
                    size={20}
                    color={Colors[colorScheme].tint}
                  />
                  <ThemedText style={{ color: Colors[colorScheme].tint }}>
                    İpucu
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>

            <ThemedText style={styles.lessonText}>
              {selectedLesson.content}
            </ThemedText>

            {selectedLesson.codeExample && (
              <ThemedView style={styles.codeContainer}>
                <ThemedText type="defaultSemiBold" style={styles.codeTitle}>
                  Kod Örneği
                </ThemedText>
                <CodeBlock code={selectedLesson.codeExample} />
              </ThemedView>
            )}

            {hasQuiz && (
              <TouchableOpacity
                style={[styles.quizButton, { backgroundColor: Colors[colorScheme].tint }]}
                onPress={handleQuizPress}
              >
                <IconSymbol
                  name="questionmark.circle.fill"
                  size={20}
                  color="white"
                />
                <ThemedText style={styles.buttonText}>
                  Quiz'i Başlat
                </ThemedText>
              </TouchableOpacity>
            )}
          </Animated.View>

          {/* İpucu Modal */}
          <Modal
            visible={showHintModal}
            transparent={true}
            animationType="fade"
            onRequestClose={handleCloseHints}
          >
            <View style={styles.modalOverlay}>
              <Animated.View
                entering={FadeIn.duration(300)}
                style={[
                  styles.hintModal,
                  { backgroundColor: Colors[colorScheme].card }
                ]}
              >
                <View style={styles.hintModalHeader}>
                  <ThemedText type="subtitle">İpucu {currentHintIndex + 1}/{selectedLesson.hints?.length}</ThemedText>
                  <TouchableOpacity onPress={handleCloseHints}>
                    <IconSymbol
                      name="xmark.circle.fill"
                      size={24}
                      color={Colors[colorScheme].icon}
                    />
                  </TouchableOpacity>
                </View>

                <ThemedText style={styles.hintText}>
                  {selectedLesson.hints?.[currentHintIndex]}
                </ThemedText>

                <TouchableOpacity
                  style={[styles.hintNextButton, { backgroundColor: Colors[colorScheme].tint }]}
                  onPress={handleNextHint}
                >
                  <ThemedText style={styles.buttonText}>
                    {currentHintIndex < (selectedLesson.hints?.length || 0) - 1 ? 'Sonraki İpucu' : 'Kapat'}
                  </ThemedText>
                  <IconSymbol
                    name={currentHintIndex < (selectedLesson.hints?.length || 0) - 1 ? 'arrow.right' : 'checkmark.circle.fill'}
                    size={20}
                    color="white"
                  />
                </TouchableOpacity>
              </Animated.View>
            </View>
          </Modal>

          {/* Navigasyon Butonları */}
          <ThemedView style={styles.navigation}>
            <TouchableOpacity
              style={[
                styles.navButton,
                { opacity: selectedLessonIndex === 0 ? 0.5 : 1 }
              ]}
              onPress={() => {
                if (selectedLessonIndex > 0) {
                  setSelectedLessonIndex(selectedLessonIndex - 1);
                }
              }}
              disabled={selectedLessonIndex === 0}
            >
              <IconSymbol
                name="arrow.left"
                size={20}
                color={Colors[colorScheme].icon}
              />
              <ThemedText style={styles.navButtonText}>Önceki</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.navButton,
                { opacity: selectedLessonIndex === module.lessons.length - 1 ? 0.5 : 1 }
              ]}
              onPress={() => {
                if (selectedLessonIndex < module.lessons.length - 1) {
                  setSelectedLessonIndex(selectedLessonIndex + 1);
                }
              }}
              disabled={selectedLessonIndex === module.lessons.length - 1}
            >
              <ThemedText style={styles.navButtonText}>Sonraki</ThemedText>
              <IconSymbol
                name="arrow.right"
                size={20}
                color={Colors[colorScheme].icon}
              />
            </TouchableOpacity>
          </ThemedView>
        </ScrollView>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.8,
  },
  metaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    marginLeft: 6,
    fontSize: 14,
  },
  lessonSelector: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  lessonTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  lessonTabText: {
    fontSize: 14,
  },
  lessonContent: {
    padding: 20,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  lessonTitle: {
    flex: 1,
  },
  hintButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  lessonText: {
    lineHeight: 24,
  },
  codeContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  codeTitle: {
    marginBottom: 8,
  },
  quizButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  navButtonText: {
    marginHorizontal: 8,
  },
  backButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  headerBackButton: {
    padding: 8,
    marginLeft: 8,
  },
  // İpucu Modal Stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  hintModal: {
    width: '100%',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  hintModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  hintText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  hintNextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
});
