import React from 'react';
import { StyleSheet, View } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type ProgressBarProps = {
  progress: number; // 0 ile 1 arasında bir değer
};

export function ProgressBar({ progress }: ProgressBarProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // İlerleme çubuğunun genişliğini animasyonlu olarak değiştir
  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${progress * 100}%`,
      backgroundColor: Colors[colorScheme].tint,
    };
  });

  return (
    <View style={[styles.container, { backgroundColor: Colors[colorScheme].border }]}>
      <Animated.View 
        style={[
          styles.progressBar,
          animatedStyle
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    width: '100%',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
});
