// This file is a fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import React from 'react';
import { OpaqueColorValue, StyleProp, ViewStyle } from 'react-native';

// Add your SFSymbol to MaterialIcons mappings here.
const MAPPING = {
  // See MaterialIcons here: https://icons.expo.fyi
  // See SF Symbols in the SF Symbols app on Mac.
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  'book.fill': 'menu_book',
  'list.bullet': 'format_list_bulleted',
  'hammer.fill': 'build',
  'person.fill': 'person',
  'star.fill': 'star',
  'star.leadinghalf.filled': 'star_half',
  'star.circle.fill': 'stars',
  'checkmark.circle.fill': 'check_circle',
  'xmark.circle.fill': 'cancel',
  'arrow.right': 'arrow_forward',
  'arrow.left': 'arrow_back',
  'arrow.up': 'arrow_upward',
  'arrow.down': 'arrow_downward',
  'doc.text.fill': 'description',
  'questionmark.circle.fill': 'help',
  'trophy.fill': 'emoji_events',
  'chart.bar.fill': 'bar_chart',
  'gear': 'settings',
  'lock.fill': 'lock',
  'lock.open.fill': 'lock_open',
  'bell.fill': 'notifications',
  'pencil': 'edit',
  'trash.fill': 'delete',
  'plus': 'add',
  'minus': 'remove',
} as Partial<
  Record<
    import('expo-symbols').SymbolViewProps['name'],
    React.ComponentProps<typeof MaterialIcons>['name']
  >
>;

export type IconSymbolName = keyof typeof MAPPING;

/**
 * An icon component that uses native SFSymbols on iOS, and MaterialIcons on Android and web. This ensures a consistent look across platforms, and optimal resource usage.
 *
 * Icon `name`s are based on SFSymbols and require manual mapping to MaterialIcons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
}) {
  return <MaterialIcons color={color} size={size} name={MAPPING[name]} style={style} />;
}
