import { DarkTheme, De<PERSON>ultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="index" options={{ headerShown: false, title: "Reactigo" }} />
        <Stack.Screen name="level-select" options={{ title: "Seviye Seçimi" }} />
        <Stack.Screen name="auth/login" options={{ title: "Giriş Yap" }} />
        <Stack.Screen name="auth/register" options={{ title: "Kayıt Ol" }} />
        <Stack.Screen name="module/[id]" options={{ title: "Modül Detayı" }} />
        <Stack.Screen name="quiz/[id]" options={{ title: "Quiz" }} />
        <Stack.Screen name="practice/[id]" options={{ title: "Uygulama" }} />
        <Stack.Screen name="+not-found" options={{ title: "Sayfa Bulunamadı" }} />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}
