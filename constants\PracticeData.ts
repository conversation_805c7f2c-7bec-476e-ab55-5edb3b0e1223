// React Native öğrenme uygulaması için uygulama görevleri
export type PracticeTask = {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number; // dakika cinsinden
  relatedModuleId: string;
  steps: PracticeStep[];
  initialCode: string;
  expectedOutput?: string;
  hints: string[];
};

export type PracticeStep = {
  id: string;
  instruction: string;
  codeHint?: string;
};

export const practiceTasks: PracticeTask[] = [
  {
    id: 'social-media-profile',
    title: 'Sosyal Medya Profili',
    description: 'Bir sosyal medya profil sayfası tasarlayın',
    difficulty: 'hard',
    duration: 40,
    relatedModuleId: 'navigation',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri import edin',
        codeHint: `import React from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';`
      },
      {
        id: 'step2',
        instruction: 'Profil verilerini tanımlayın',
        codeHint: `// Kullanıcı verileri
const userData = {
  username: 'reactnative_dev',
  name: 'React Native Developer',
  avatar: 'https://randomuser.me/api/portraits/women/40.jpg',
  bio: 'React Native geliştirici | UI/UX meraklısı | Mobil uygulama tasarımcısı',
  stats: {
    posts: 42,
    followers: 1024,
    following: 256
  }
};

// Gönderi verileri
const posts = [
  { id: '1', imageUrl: 'https://picsum.photos/id/1/300/300', likes: 128, comments: 24 },
  { id: '2', imageUrl: 'https://picsum.photos/id/10/300/300', likes: 256, comments: 32 },
  { id: '3', imageUrl: 'https://picsum.photos/id/20/300/300', likes: 512, comments: 64 },
  { id: '4', imageUrl: 'https://picsum.photos/id/30/300/300', likes: 64, comments: 16 },
  { id: '5', imageUrl: 'https://picsum.photos/id/40/300/300', likes: 128, comments: 32 },
  { id: '6', imageUrl: 'https://picsum.photos/id/50/300/300', likes: 256, comments: 48 },
];`
      },
      {
        id: 'step3',
        instruction: 'Profil başlığı bileşenini oluşturun',
        codeHint: `function ProfileHeader({ user }) {
  return (
    <View style={styles.profileHeader}>
      <Image source={{ uri: user.avatar }} style={styles.avatar} />

      <View style={styles.userInfo}>
        <Text style={styles.name}>{user.name}</Text>
        <Text style={styles.username}>@{user.username}</Text>
        <Text style={styles.bio}>{user.bio}</Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{user.stats.posts}</Text>
          <Text style={styles.statLabel}>Gönderiler</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{user.stats.followers}</Text>
          <Text style={styles.statLabel}>Takipçiler</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{user.stats.following}</Text>
          <Text style={styles.statLabel}>Takip</Text>
        </View>
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.followButton}>
          <Text style={styles.followButtonText}>Takip Et</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.messageButton}>
          <Text style={styles.messageButtonText}>Mesaj</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}`
      },
      {
        id: 'step4',
        instruction: 'Gönderi grid\'ini oluşturun',
        codeHint: `function PostGrid({ posts }) {
  return (
    <View style={styles.postGrid}>
      {posts.map(post => (
        <TouchableOpacity key={post.id} style={styles.postItem}>
          <Image source={{ uri: post.imageUrl }} style={styles.postImage} />
          <View style={styles.postStats}>
            <View style={styles.postStat}>
              <Ionicons name="heart" size={14} color="white" />
              <Text style={styles.postStatText}>{post.likes}</Text>
            </View>
            <View style={styles.postStat}>
              <Ionicons name="chatbubble" size={14} color="white" />
              <Text style={styles.postStatText}>{post.comments}</Text>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
}`
      },
      {
        id: 'step5',
        instruction: 'Ana profil bileşenini oluşturun',
        codeHint: `function SocialMediaProfile() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="arrow-back" size={24} color="black" />
        <Text style={styles.headerTitle}>Profil</Text>
        <Ionicons name="ellipsis-horizontal" size={24} color="black" />
      </View>

      <ProfileHeader user={userData} />

      <View style={styles.tabBar}>
        <TouchableOpacity style={[styles.tab, styles.activeTab]}>
          <Ionicons name="grid" size={24} color="black" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.tab}>
          <Ionicons name="list" size={24} color="gray" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.tab}>
          <Ionicons name="bookmark" size={24} color="gray" />
        </TouchableOpacity>
      </View>

      <PostGrid posts={posts} />
    </ScrollView>
  );
}`
      },
      {
        id: 'step6',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  profileHeader: {
    padding: 20,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 15,
  },
  userInfo: {
    marginBottom: 15,
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  username: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  bio: {
    fontSize: 14,
    lineHeight: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#eee',
    paddingVertical: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  followButton: {
    flex: 1,
    backgroundColor: '#3897f0',
    paddingVertical: 8,
    borderRadius: 5,
    marginRight: 5,
    alignItems: 'center',
  },
  followButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  messageButton: {
    flex: 1,
    backgroundColor: '#efefef',
    paddingVertical: 8,
    borderRadius: 5,
    marginLeft: 5,
    alignItems: 'center',
  },
  messageButtonText: {
    fontWeight: 'bold',
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#eee',
    paddingVertical: 10,
  },
  tab: {
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: 'black',
  },
  postGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 2,
  },
  postItem: {
    width: '33.33%',
    aspectRatio: 1,
    padding: 2,
    position: 'relative',
  },
  postImage: {
    width: '100%',
    height: '100%',
  },
  postStats: {
    position: 'absolute',
    bottom: 5,
    left: 5,
    flexDirection: 'row',
  },
  postStat: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 10,
  },
  postStatText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 3,
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri import edin

// Profil ve gönderi verilerini tanımlayın

// Profil başlığı bileşenini oluşturun

// Gönderi grid'ini oluşturun

// Ana profil bileşenini oluşturun
function SocialMediaProfile() {
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default SocialMediaProfile;`,
    hints: [
      'ScrollView ile uzun içeriği kaydırılabilir yapabilirsiniz',
      'Expo Vector Icons kütüphanesini kullanarak ikonlar ekleyebilirsiniz',
      'flexDirection: "row" ve flexWrap: "wrap" ile grid düzeni oluşturabilirsiniz',
      'position: "absolute" ile öğeleri üst üste bindirebilirsiniz',
      'aspectRatio: 1 ile kare şeklinde öğeler oluşturabilirsiniz'
    ]
  },
  {
    id: 'flex-layout-practice',
    title: 'Flex Layout Alıştırması',
    description: 'React Native\'de Flexbox kullanarak farklı layout düzenleri oluşturun',
    difficulty: 'easy',
    duration: 15,
    relatedModuleId: 'intro-to-react-native',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri import edin',
        codeHint: 'import { View, StyleSheet } from "react-native";'
      },
      {
        id: 'step2',
        instruction: 'Üç farklı renkli kutu içeren bir container oluşturun',
        codeHint: `function FlexLayoutExample() {
  return (
    <View style={styles.container}>
      <View style={styles.redBox} />
      <View style={styles.greenBox} />
      <View style={styles.blueBox} />
    </View>
  );
}`
      },
      {
        id: 'step3',
        instruction: 'StyleSheet ile stilleri tanımlayın (kutuları yan yana dizin)',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#F5FCFF',
  },
  redBox: {
    width: 100,
    height: 100,
    backgroundColor: 'red',
  },
  greenBox: {
    width: 100,
    height: 100,
    backgroundColor: 'green',
  },
  blueBox: {
    width: 100,
    height: 100,
    backgroundColor: 'blue',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri import edin

function FlexLayoutExample() {
  // Üç farklı renkli kutu içeren bir container oluşturun
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default FlexLayoutExample;`,
    hints: [
      'flexDirection: "row" ile öğeleri yatay olarak sıralayabilirsiniz',
      'justifyContent: "space-around" ile öğeler arasında eşit boşluk bırakabilirsiniz',
      'alignItems: "center" ile öğeleri dikey eksende ortalayabilirsiniz',
      'flex: 1 ile container\'ın mevcut alanı doldurmasını sağlayabilirsiniz',
      'width ve height ile kutuların boyutlarını belirleyebilirsiniz'
    ]
  },
  {
    id: 'image-gallery',
    title: 'Resim Galerisi',
    description: 'Basit bir resim galerisi uygulaması oluşturun',
    difficulty: 'medium',
    duration: 25,
    relatedModuleId: 'components-and-props',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri import edin',
        codeHint: `import React from 'react';
import { View, Image, ScrollView, StyleSheet, Dimensions } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'Resim verilerini tanımlayın',
        codeHint: `const imageData = [
  { id: '1', source: { uri: 'https://picsum.photos/id/10/300/300' } },
  { id: '2', source: { uri: 'https://picsum.photos/id/20/300/300' } },
  { id: '3', source: { uri: 'https://picsum.photos/id/30/300/300' } },
  { id: '4', source: { uri: 'https://picsum.photos/id/40/300/300' } },
  { id: '5', source: { uri: 'https://picsum.photos/id/50/300/300' } },
  { id: '6', source: { uri: 'https://picsum.photos/id/60/300/300' } },
];`
      },
      {
        id: 'step3',
        instruction: 'Galeri bileşenini oluşturun',
        codeHint: `function ImageGallery() {
  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.imageGrid}>
          {imageData.map((image) => (
            <View key={image.id} style={styles.imageContainer}>
              <Image source={image.source} style={styles.image} />
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}`
      },
      {
        id: 'step4',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const { width } = Dimensions.get('window');
const imageSize = width / 2 - 15;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    padding: 10,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  imageContainer: {
    marginBottom: 10,
  },
  image: {
    width: imageSize,
    height: imageSize,
    borderRadius: 10,
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri import edin

// Resim verilerini tanımlayın

function ImageGallery() {
  // Galeri bileşenini oluşturun
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default ImageGallery;`,
    hints: [
      'Dimensions.get("window") ile ekran boyutlarını alabilirsiniz',
      'flexWrap: "wrap" ile öğelerin sığmadığında alt satıra geçmesini sağlayabilirsiniz',
      'ScrollView ile içeriğin kaydırılabilir olmasını sağlayabilirsiniz',
      'map() fonksiyonu ile dizi elemanlarını döngüye alabilirsiniz',
      'borderRadius ile resimlerin köşelerini yuvarlayabilirsiniz'
    ]
  },
  {
    id: 'animated-button',
    title: 'Animasyonlu Buton',
    description: 'React Native Animated API kullanarak basit bir animasyonlu buton oluşturun',
    difficulty: 'medium',
    duration: 20,
    relatedModuleId: 'jsx-basics',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri ve Animated API\'yi import edin',
        codeHint: `import React, { useState, useRef } from 'react';
import { View, Text, TouchableWithoutFeedback, Animated, StyleSheet } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'Animated değerini oluşturun ve başlangıç değerini atayın',
        codeHint: `function AnimatedButton() {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Fonksiyonlar burada olacak

  // Return JSX burada olacak
}`
      },
      {
        id: 'step3',
        instruction: 'Buton basıldığında ve bırakıldığında çalışacak animasyon fonksiyonlarını oluşturun',
        codeHint: `const handlePressIn = () => {
  Animated.spring(scaleAnim, {
    toValue: 0.9,
    useNativeDriver: true,
  }).start();
};

const handlePressOut = () => {
  Animated.spring(scaleAnim, {
    toValue: 1,
    friction: 3,
    tension: 40,
    useNativeDriver: true,
  }).start();
};`
      },
      {
        id: 'step4',
        instruction: 'Animasyonlu butonu oluşturun',
        codeHint: `return (
  <View style={styles.container}>
    <TouchableWithoutFeedback
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
    >
      <Animated.View
        style={[
          styles.button,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Text style={styles.buttonText}>Bana Tıkla!</Text>
      </Animated.View>
    </TouchableWithoutFeedback>
  </View>
);`
      },
      {
        id: 'step5',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#61DAFB',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri ve Animated API'yi import edin

function AnimatedButton() {
  // Animated değerini oluşturun

  // Animasyon fonksiyonlarını oluşturun

  // Animasyonlu butonu oluşturun
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default AnimatedButton;`,
    hints: [
      'useRef hook\'u ile Animated.Value değerini oluşturabilirsiniz',
      'Animated.spring() ile yaylı animasyonlar oluşturabilirsiniz',
      'transform: [{ scale: scaleAnim }] ile ölçeklendirme animasyonu yapabilirsiniz',
      'TouchableWithoutFeedback, dokunma olaylarını yakalarken görsel geri bildirim vermez',
      'useNativeDriver: true ile animasyonların native thread\'de çalışmasını sağlayabilirsiniz'
    ]
  },
  {
    id: 'hello-world-app',
    title: 'Merhaba Dünya Uygulaması',
    description: 'Basit bir "Merhaba Dünya" uygulaması oluşturun',
    difficulty: 'easy',
    duration: 10,
    relatedModuleId: 'intro-to-react-native',
    steps: [
      {
        id: 'step1',
        instruction: 'View ve Text bileşenlerini import edin',
        codeHint: 'import { View, Text } from "react-native";'
      },
      {
        id: 'step2',
        instruction: 'App bileşenini oluşturun ve ekranın ortasında "Merhaba Dünya" yazısını gösterin',
        codeHint: `function App() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Merhaba Dünya</Text>
    </View>
  );
}`
      },
      {
        id: 'step3',
        instruction: 'App bileşenini export edin',
        codeHint: 'export default App;'
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri import edin

// App bileşenini oluşturun

// App bileşenini export edin
`,
    expectedOutput: `import React from 'react';
import { View, Text } from 'react-native';

function App() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Merhaba Dünya</Text>
    </View>
  );
}

export default App;`,
    hints: [
      'React Native\'de temel bileşenler View ve Text\'tir',
      'View, bir container bileşenidir ve diğer bileşenleri içerebilir',
      'Text, metin göstermek için kullanılır',
      'flex: 1 stili, bileşenin mevcut alanı doldurmasını sağlar',
      'justifyContent ve alignItems, içeriği ortalamak için kullanılır'
    ]
  },
  {
    id: 'styled-button',
    title: 'Özel Stil Buton',
    description: 'Özel stiller ile bir buton bileşeni oluşturun',
    difficulty: 'easy',
    duration: 15,
    relatedModuleId: 'jsx-basics',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri import edin (View, Text, TouchableOpacity, StyleSheet)',
        codeHint: 'import { View, Text, TouchableOpacity, StyleSheet } from "react-native";'
      },
      {
        id: 'step2',
        instruction: 'CustomButton adında bir bileşen oluşturun ve props olarak title ve onPress alın',
        codeHint: `function CustomButton({ title, onPress }) {
  return (
    <TouchableOpacity style={styles.button} onPress={onPress}>
      <Text style={styles.buttonText}>{title}</Text>
    </TouchableOpacity>
  );
}`
      },
      {
        id: 'step3',
        instruction: 'App bileşeni içinde CustomButton bileşenini kullanın',
        codeHint: `function App() {
  const handlePress = () => {
    alert('Butona tıklandı!');
  };

  return (
    <View style={styles.container}>
      <CustomButton title="Tıkla Bana" onPress={handlePress} />
    </View>
  );
}`
      },
      {
        id: 'step4',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#61DAFB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri import edin

// CustomButton bileşenini oluşturun

// App bileşenini oluşturun

// Stilleri tanımlayın

export default App;`,
    hints: [
      'TouchableOpacity, dokunma efekti olan bir buton oluşturmak için kullanılır',
      'StyleSheet.create() ile stilleri tanımlamak performans açısından daha iyidir',
      'Props\'ları destructuring ile alabilirsiniz: { title, onPress }',
      'alert() fonksiyonu ile basit bir uyarı gösterebilirsiniz',
      'borderRadius ile butonun köşelerini yuvarlayabilirsiniz'
    ]
  },
  {
    id: 'todo-list-app',
    title: 'Basit Yapılacaklar Listesi',
    description: 'Görev ekleyip silebileceğiniz basit bir yapılacaklar listesi uygulaması oluşturun',
    difficulty: 'medium',
    duration: 30,
    relatedModuleId: 'components-and-props',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri ve useState hook\'unu import edin',
        codeHint: `import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, FlatList, StyleSheet } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'Görevleri ve yeni görev metnini tutmak için state\'leri oluşturun',
        codeHint: `function TodoApp() {
  const [tasks, setTasks] = useState([]);
  const [taskText, setTaskText] = useState('');

  // Fonksiyonlar burada olacak

  // Return JSX burada olacak
}`
      },
      {
        id: 'step3',
        instruction: 'Görev ekleme ve silme fonksiyonlarını oluşturun',
        codeHint: `const addTask = () => {
  if (taskText.trim() !== '') {
    setTasks([...tasks, { id: Date.now().toString(), text: taskText }]);
    setTaskText('');
  }
};

const deleteTask = (id) => {
  setTasks(tasks.filter(task => task.id !== id));
};`
      },
      {
        id: 'step4',
        instruction: 'Görev girişi ve görev listesi için UI oluşturun',
        codeHint: `return (
  <View style={styles.container}>
    <Text style={styles.title}>Yapılacaklar Listesi</Text>

    <View style={styles.inputContainer}>
      <TextInput
        style={styles.input}
        placeholder="Yeni görev ekle..."
        value={taskText}
        onChangeText={setTaskText}
      />
      <TouchableOpacity style={styles.addButton} onPress={addTask}>
        <Text style={styles.addButtonText}>Ekle</Text>
      </TouchableOpacity>
    </View>

    <FlatList
      data={tasks}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <View style={styles.taskItem}>
          <Text style={styles.taskText}>{item.text}</Text>
          <TouchableOpacity onPress={() => deleteTask(item.id)}>
            <Text style={styles.deleteButton}>Sil</Text>
          </TouchableOpacity>
        </View>
      )}
    />
  </View>
);`
      },
      {
        id: 'step5',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    marginTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  addButton: {
    backgroundColor: '#61DAFB',
    paddingHorizontal: 15,
    paddingVertical: 7,
    borderRadius: 5,
    marginLeft: 10,
    justifyContent: 'center',
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  taskText: {
    flex: 1,
  },
  deleteButton: {
    color: 'red',
    fontWeight: 'bold',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri ve hook'ları import edin

function TodoApp() {
  // State'leri tanımlayın

  // Görev ekleme ve silme fonksiyonlarını oluşturun

  // UI'ı oluşturun
}

// Stilleri tanımlayın

export default TodoApp;`,
    hints: [
      'useState hook\'u ile bileşen içinde state yönetebilirsiniz',
      'FlatList, performanslı bir şekilde liste göstermek için kullanılır',
      'TextInput, kullanıcıdan metin girişi almak için kullanılır',
      'Date.now() ile benzersiz ID\'ler oluşturabilirsiniz',
      'Array spread operatörü [...array] ile mevcut diziyi kopyalayabilirsiniz',
      'filter() metodu ile diziden eleman çıkarabilirsiniz'
    ]
  },
  {
    id: 'form-validation',
    title: 'Form Doğrulama',
    description: 'Giriş formu oluşturun ve form doğrulama işlemlerini gerçekleştirin',
    difficulty: 'medium',
    duration: 30,
    relatedModuleId: 'components-and-props',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri ve useState hook\'unu import edin',
        codeHint: `import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'Form state\'lerini ve hata mesajlarını tanımlayın',
        codeHint: `function FormValidation() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isFormValid, setIsFormValid] = useState(false);

  // Fonksiyonlar burada olacak

  // Return JSX burada olacak
}`
      },
      {
        id: 'step3',
        instruction: 'E-posta ve şifre doğrulama fonksiyonlarını oluşturun',
        codeHint: `const validateEmail = (text) => {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  if (!text) {
    setEmailError('E-posta alanı boş bırakılamaz');
    return false;
  } else if (!emailRegex.test(text)) {
    setEmailError('Geçerli bir e-posta adresi girin');
    return false;
  } else {
    setEmailError('');
    return true;
  }
};

const validatePassword = (text) => {
  if (!text) {
    setPasswordError('Şifre alanı boş bırakılamaz');
    return false;
  } else if (text.length < 6) {
    setPasswordError('Şifre en az 6 karakter olmalıdır');
    return false;
  } else {
    setPasswordError('');
    return true;
  }
};

const handleSubmit = () => {
  const isEmailValid = validateEmail(email);
  const isPasswordValid = validatePassword(password);

  if (isEmailValid && isPasswordValid) {
    alert('Form başarıyla gönderildi!');
    // Burada form gönderme işlemleri yapılabilir
  }
};`
      },
      {
        id: 'step4',
        instruction: 'Form UI\'ını oluşturun',
        codeHint: `return (
  <View style={styles.container}>
    <Text style={styles.title}>Giriş Formu</Text>

    <View style={styles.formGroup}>
      <Text style={styles.label}>E-posta</Text>
      <TextInput
        style={styles.input}
        value={email}
        onChangeText={(text) => {
          setEmail(text);
          validateEmail(text);
        }}
        placeholder="E-posta adresinizi girin"
        keyboardType="email-address"
        autoCapitalize="none"
      />
      {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
    </View>

    <View style={styles.formGroup}>
      <Text style={styles.label}>Şifre</Text>
      <TextInput
        style={styles.input}
        value={password}
        onChangeText={(text) => {
          setPassword(text);
          validatePassword(text);
        }}
        placeholder="Şifrenizi girin"
        secureTextEntry
      />
      {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
    </View>

    <TouchableOpacity
      style={[
        styles.submitButton,
        (!email || !password || emailError || passwordError) && styles.disabledButton
      ]}
      onPress={handleSubmit}
      disabled={!email || !password || emailError || passwordError}
    >
      <Text style={styles.submitButtonText}>Giriş Yap</Text>
    </TouchableOpacity>
  </View>
);`
      },
      {
        id: 'step5',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
  submitButton: {
    backgroundColor: '#61DAFB',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri ve useState hook'unu import edin

function FormValidation() {
  // Form state'lerini ve hata mesajlarını tanımlayın

  // E-posta ve şifre doğrulama fonksiyonlarını oluşturun

  // Form UI'ını oluşturun
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default FormValidation;`,
    hints: [
      'useState hook\'u ile form alanlarının değerlerini ve hata mesajlarını yönetebilirsiniz',
      'Regular expression (regex) ile e-posta formatını doğrulayabilirsiniz',
      'onChangeText prop\'u ile TextInput değişikliklerini yakalayabilirsiniz',
      'Koşullu render için ternary operatörü (condition ? trueCase : falseCase) kullanabilirsiniz',
      'disabled prop\'u ile butonun etkin olup olmadığını kontrol edebilirsiniz'
    ]
  },
  {
    id: 'dark-mode-toggle',
    title: 'Karanlık Mod Geçişi',
    description: 'Açık ve karanlık tema arasında geçiş yapabilen bir uygulama oluşturun',
    difficulty: 'medium',
    duration: 25,
    relatedModuleId: 'components-and-props',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri ve useState hook\'unu import edin',
        codeHint: `import React, { useState } from 'react';
import { View, Text, Switch, StyleSheet } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'Tema state\'ini tanımlayın',
        codeHint: `function DarkModeToggle() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Tema değiştirme fonksiyonu burada olacak

  // Return JSX burada olacak
}`
      },
      {
        id: 'step3',
        instruction: 'Tema değiştirme fonksiyonunu oluşturun',
        codeHint: `const toggleTheme = () => {
  setIsDarkMode(previousState => !previousState);
};`
      },
      {
        id: 'step4',
        instruction: 'Tema değiştirme UI\'ını oluşturun',
        codeHint: `// Tema renklerini tanımlayın
const theme = {
  backgroundColor: isDarkMode ? '#121212' : '#FFFFFF',
  textColor: isDarkMode ? '#FFFFFF' : '#121212',
  cardColor: isDarkMode ? '#1E1E1E' : '#F5F5F5',
  switchTrackColor: { false: '#767577', true: '#81b0ff' },
  switchThumbColor: isDarkMode ? '#f5dd4b' : '#f4f3f4',
};

return (
  <View style={[styles.container, { backgroundColor: theme.backgroundColor }]}>
    <Text style={[styles.title, { color: theme.textColor }]}>
      {isDarkMode ? 'Karanlık Mod' : 'Aydınlık Mod'}
    </Text>

    <View style={[styles.card, { backgroundColor: theme.cardColor }]}>
      <Text style={[styles.cardText, { color: theme.textColor }]}>
        Bu bir örnek karttır. Tema değiştiğinde renkleri de değişir.
      </Text>
    </View>

    <View style={styles.switchContainer}>
      <Text style={[styles.switchText, { color: theme.textColor }]}>
        {isDarkMode ? 'Aydınlık Moda Geç' : 'Karanlık Moda Geç'}
      </Text>
      <Switch
        trackColor={theme.switchTrackColor}
        thumbColor={theme.switchThumbColor}
        ios_backgroundColor="#3e3e3e"
        onValueChange={toggleTheme}
        value={isDarkMode}
      />
    </View>
  </View>
);`
      },
      {
        id: 'step5',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  card: {
    width: '100%',
    padding: 20,
    borderRadius: 10,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardText: {
    fontSize: 16,
    lineHeight: 24,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  switchText: {
    marginRight: 10,
    fontSize: 16,
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri ve useState hook'unu import edin

function DarkModeToggle() {
  // Tema state'ini tanımlayın

  // Tema değiştirme fonksiyonunu oluşturun

  // Tema değiştirme UI'ını oluşturun
  return (
    // Kodunuzu buraya yazın
  );
}

// Stilleri tanımlayın

export default DarkModeToggle;`,
    hints: [
      'useState hook\'u ile tema durumunu yönetebilirsiniz',
      'Switch bileşeni ile açma/kapama düğmesi oluşturabilirsiniz',
      'Koşullu stil uygulamak için array içinde stil nesneleri kullanabilirsiniz: [styles.base, condition && styles.active]',
      'trackColor ve thumbColor prop\'ları ile Switch\'in renklerini özelleştirebilirsiniz',
      'Tema değişikliğini tüm uygulamaya yaymak için Context API kullanabilirsiniz (ileri seviye)'
    ]
  },
  {
    id: 'weather-app',
    title: 'Hava Durumu Uygulaması',
    description: 'Basit bir hava durumu uygulaması oluşturun',
    difficulty: 'hard',
    duration: 45,
    relatedModuleId: 'navigation',
    steps: [
      {
        id: 'step1',
        instruction: 'Gerekli bileşenleri ve hook\'ları import edin',
        codeHint: `import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, ActivityIndicator, StyleSheet } from 'react-native';`
      },
      {
        id: 'step2',
        instruction: 'State\'leri tanımlayın: şehir, hava durumu verileri ve yükleniyor durumu',
        codeHint: `function WeatherApp() {
  const [city, setCity] = useState('Istanbul');
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Fonksiyonlar ve useEffect burada olacak

  // Return JSX burada olacak
}`
      },
      {
        id: 'step3',
        instruction: 'Hava durumu verilerini almak için bir fonksiyon oluşturun',
        codeHint: `const fetchWeatherData = async () => {
  try {
    setLoading(true);
    // Gerçek bir API kullanmak yerine örnek veri döndürelim
    setTimeout(() => {
      const mockData = {
        city: city,
        temperature: Math.floor(Math.random() * 30) + 5, // 5-35 arası rastgele sıcaklık
        condition: ['Güneşli', 'Bulutlu', 'Yağmurlu', 'Karlı'][Math.floor(Math.random() * 4)],
        humidity: Math.floor(Math.random() * 50) + 30, // 30-80 arası nem
        windSpeed: Math.floor(Math.random() * 30) + 5, // 5-35 km/s rüzgar hızı
      };
      setWeatherData(mockData);
      setLoading(false);
    }, 1000);
  } catch (error) {
    console.error('Hava durumu verileri alınamadı:', error);
    setLoading(false);
  }
};`
      },
      {
        id: 'step4',
        instruction: 'useEffect ile sayfa yüklendiğinde hava durumu verilerini alın',
        codeHint: `useEffect(() => {
  fetchWeatherData();
}, []);`
      },
      {
        id: 'step5',
        instruction: 'Şehir arama formu ve hava durumu gösterimi için UI oluşturun',
        codeHint: `return (
  <View style={styles.container}>
    <Text style={styles.title}>Hava Durumu</Text>

    <View style={styles.searchContainer}>
      <TextInput
        style={styles.input}
        placeholder="Şehir adı girin..."
        value={city}
        onChangeText={setCity}
      />
      <TouchableOpacity style={styles.searchButton} onPress={fetchWeatherData}>
        <Text style={styles.searchButtonText}>Ara</Text>
      </TouchableOpacity>
    </View>

    {loading ? (
      <ActivityIndicator size="large" color="#61DAFB" style={styles.loader} />
    ) : weatherData ? (
      <View style={styles.weatherContainer}>
        <Text style={styles.cityName}>{weatherData.city}</Text>

        <View style={styles.weatherInfo}>
          <Text style={styles.temperature}>{weatherData.temperature}°C</Text>
          <Text style={styles.condition}>{weatherData.condition}</Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Nem</Text>
            <Text style={styles.detailValue}>{weatherData.humidity}%</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Rüzgar</Text>
            <Text style={styles.detailValue}>{weatherData.windSpeed} km/s</Text>
          </View>
        </View>
      </View>
    ) : (
      <Text style={styles.errorText}>Hava durumu verileri alınamadı.</Text>
    )}
  </View>
);`
      },
      {
        id: 'step6',
        instruction: 'StyleSheet ile stilleri tanımlayın',
        codeHint: `const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 5,
    fontSize: 16,
  },
  searchButton: {
    backgroundColor: '#61DAFB',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 5,
    marginLeft: 10,
    justifyContent: 'center',
  },
  searchButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loader: {
    marginTop: 50,
  },
  weatherContainer: {
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    padding: 20,
    marginTop: 20,
  },
  cityName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  weatherInfo: {
    alignItems: 'center',
    marginVertical: 20,
  },
  temperature: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  condition: {
    fontSize: 20,
    marginTop: 5,
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 5,
  },
  errorText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: 'red',
  },
});`
      }
    ],
    initialCode: `import React from 'react';
// Gerekli bileşenleri ve hook'ları import edin

function WeatherApp() {
  // State'leri tanımlayın

  // Hava durumu verilerini almak için fonksiyon oluşturun

  // useEffect ile sayfa yüklendiğinde verileri alın

  // UI'ı oluşturun
}

// Stilleri tanımlayın

export default WeatherApp;`,
    hints: [
      'useState ile bileşen state\'lerini yönetebilirsiniz',
      'useEffect, bileşen yüklendiğinde veya bağımlılıklar değiştiğinde çalışır',
      'ActivityIndicator, yükleme durumunu göstermek için kullanılır',
      'Gerçek bir uygulamada, fetch veya axios ile API\'dan veri alabilirsiniz',
      'Koşullu render için ternary operatörü (condition ? trueCase : falseCase) kullanabilirsiniz',
      'StyleSheet.create() ile stilleri tanımlamak performans açısından daha iyidir'
    ]
  }
];

// Uygulama görevi ID'sine göre görevi getir
export function getPracticeTaskById(id: string): PracticeTask | undefined {
  return practiceTasks.find(task => task.id === id);
}

// İlgili modül ID'sine göre uygulama görevlerini getir
export function getPracticeTasksByModuleId(moduleId: string): PracticeTask[] {
  return practiceTasks.filter(task => task.relatedModuleId === moduleId);
}

// Zorluk seviyesine göre uygulama görevlerini getir
export function getPracticeTasksByDifficulty(difficulty: 'easy' | 'medium' | 'hard'): PracticeTask[] {
  return practiceTasks.filter(task => task.difficulty === difficulty);
}
