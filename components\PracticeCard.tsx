import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { PracticeTask } from '@/constants/PracticeData';

type PracticeCardProps = {
  task: PracticeTask;
  onPress: () => void;
};

export function PracticeCard({ task, onPress }: PracticeCardProps) {
  const colorScheme = useColorScheme() ?? 'light';
  
  // Zorluk seviyesine göre renk ve ikon belirle
  const getDifficultyInfo = () => {
    switch (task.difficulty) {
      case 'easy':
        return {
          color: Colors[colorScheme].success,
          icon: 'star.fill',
          text: 'Kolay'
        };
      case 'medium':
        return {
          color: Colors[colorScheme].warning,
          icon: 'star.leadinghalf.filled',
          text: 'Orta'
        };
      case 'hard':
        return {
          color: Colors[colorScheme].error,
          icon: 'star.circle.fill',
          text: 'Zor'
        };
      default:
        return {
          color: Colors[colorScheme].success,
          icon: 'star.fill',
          text: 'Kolay'
        };
    }
  };

  const difficultyInfo = getDifficultyInfo();

  return (
    <TouchableOpacity
      style={[
        styles.card,
        { backgroundColor: Colors[colorScheme].practiceCard }
      ]}
      onPress={onPress}
    >
      <View style={styles.contentContainer}>
        <ThemedText type="defaultSemiBold" style={styles.title}>
          {task.title}
        </ThemedText>
        
        <ThemedText style={styles.description}>
          {task.description}
        </ThemedText>
        
        <View style={styles.metaContainer}>
          <View style={styles.difficultyContainer}>
            <IconSymbol
              name={difficultyInfo.icon as any}
              size={16}
              color={difficultyInfo.color}
            />
            <ThemedText style={[styles.difficultyText, { color: difficultyInfo.color }]}>
              {difficultyInfo.text}
            </ThemedText>
          </View>
          
          <ThemedText style={styles.duration}>
            {task.duration} dakika
          </ThemedText>
        </View>
      </View>
      
      <View style={styles.iconContainer}>
        <IconSymbol
          name="hammer.fill"
          size={24}
          color={Colors[colorScheme].text}
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.8,
  },
  metaContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyText: {
    fontSize: 12,
    marginLeft: 4,
  },
  duration: {
    fontSize: 12,
    opacity: 0.6,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
});
