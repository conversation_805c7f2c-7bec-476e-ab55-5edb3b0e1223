/**
 * React Native Öğrenme Uygulaması için renk şeması
 * Açık ve koyu tema için renkler tanımlanmıştır
 */

const tintColorLight = '#61DAFB'; // React Native mavi
const tintColorDark = '#61DAFB';  // Koyu temada da aynı mavi

export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    card: '#F5F5F5',
    border: '#E0E0E0',
    success: '#4CAF50',
    error: '#F44336',
    warning: '#FF9800',
    info: '#2196F3',
    moduleCard: '#E3F2FD',
    quizCard: '#E8F5E9',
    practiceCard: '#FFF3E0',
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    card: '#1E1E1E',
    border: '#333333',
    success: '#388E3C',
    error: '#D32F2F',
    warning: '#F57C00',
    info: '#1976D2',
    moduleCard: '#0D47A1',
    quizCard: '#1B5E20',
    practiceCard: '#E65100',
  },
};
