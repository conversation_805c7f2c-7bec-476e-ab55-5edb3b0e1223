import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View, ScrollView, TextInput } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { getPracticeTaskById } from '@/constants/PracticeData';
import { CodeBlock } from '@/components/CodeBlock';
import { InteractiveCodeEditor } from '@/components/InteractiveCodeEditor';

export default function PracticeScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme() ?? 'light';
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [code, setCode] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [completed, setCompleted] = useState(false);

  const task = getPracticeTaskById(id || '');

  useEffect(() => {
    if (task) {
      setCode(task.initialCode);
    }
  }, [task]);

  if (!task) {
    return (
      <>
        <Stack.Screen options={{ title: 'Görev Bulunamadı' }} />
        <ThemedView style={styles.container}>
          <ThemedText>Uygulama görevi bulunamadı.</ThemedText>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: Colors[colorScheme].tint }]}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.buttonText}>Geri Dön</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </>
    );
  }

  const currentStep = task.steps[currentStepIndex];

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
  };

  const handleNextStep = () => {
    if (currentStepIndex < task.steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
      setShowHint(false);
    } else {
      setCompleted(true);
    }
  };

  const handlePrevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
      setShowHint(false);
    }
  };

  const handleTogglePreview = () => {
    setShowPreview(!showPreview);
  };

  const handleToggleHint = () => {
    setShowHint(!showHint);
  };

  const handleFinish = () => {
    // Gerçek uygulamada burada görev tamamlama kaydedilir
    router.back();
  };

  if (completed) {
    return (
      <>
        <Stack.Screen options={{ title: 'Görev Tamamlandı' }} />
        <ThemedView style={styles.container}>
          <Animated.View
            entering={FadeInDown.duration(1000)}
            style={styles.completedContainer}
          >
            <View style={[
              styles.completedIconContainer,
              { backgroundColor: Colors[colorScheme].success }
            ]}>
              <IconSymbol
                name="checkmark.circle.fill"
                size={60}
                color="white"
              />
            </View>

            <ThemedText type="title" style={styles.completedTitle}>
              Tebrikler!
            </ThemedText>

            <ThemedText style={styles.completedMessage}>
              "{task.title}" görevini başarıyla tamamladınız. Harika iş çıkardınız!
            </ThemedText>

            <TouchableOpacity
              style={[styles.completedButton, { backgroundColor: Colors[colorScheme].success }]}
              onPress={handleFinish}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={20}
                color="white"
              />
              <ThemedText style={styles.buttonText}>
                Tamamla
              </ThemedText>
            </TouchableOpacity>
          </Animated.View>
        </ThemedView>
      </>
    );
  }

  return (
    <>
      <Stack.Screen options={{
        title: task.title,
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerBackButton}>
            <IconSymbol
              name="arrow.left"
              size={24}
              color={Colors[colorScheme].text}
            />
          </TouchableOpacity>
        )
      }} />
      <ThemedView style={styles.container}>
        {/* Görev Başlığı ve Açıklaması */}
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>
            {task.title}
          </ThemedText>
          <ThemedText style={styles.description}>
            {task.description}
          </ThemedText>

          <ThemedView style={styles.metaContainer}>
            <View style={styles.metaItem}>
              <IconSymbol
                name="clock.fill"
                size={16}
                color={Colors[colorScheme].icon}
              />
              <ThemedText style={styles.metaText}>
                {task.duration} dakika
              </ThemedText>
            </View>

            <View style={styles.metaItem}>
              <IconSymbol
                name={task.difficulty === 'easy' ? 'star.fill' :
                      task.difficulty === 'medium' ? 'star.leadinghalf.filled' : 'star.circle.fill'}
                size={16}
                color={task.difficulty === 'easy' ? Colors[colorScheme].success :
                       task.difficulty === 'medium' ? Colors[colorScheme].warning : Colors[colorScheme].error}
              />
              <ThemedText style={styles.metaText}>
                {task.difficulty === 'easy' ? 'Kolay' :
                 task.difficulty === 'medium' ? 'Orta' : 'Zor'}
              </ThemedText>
            </View>
          </ThemedView>
        </ThemedView>

        {/* İlerleme */}
        <ThemedView style={styles.progressContainer}>
          <ThemedText style={styles.progressText}>
            Adım {currentStepIndex + 1}/{task.steps.length}
          </ThemedText>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${((currentStepIndex + 1) / task.steps.length) * 100}%`,
                  backgroundColor: Colors[colorScheme].tint
                }
              ]}
            />
          </View>
        </ThemedView>

        {/* Adım Talimatı */}
        <Animated.View
          entering={FadeIn.duration(500)}
          style={styles.stepContainer}
        >
          <ThemedText type="defaultSemiBold" style={styles.stepTitle}>
            Adım {currentStepIndex + 1}: {currentStep.instruction}
          </ThemedText>

          <View style={styles.hintContainer}>
            <TouchableOpacity
              style={styles.hintButton}
              onPress={handleToggleHint}
            >
              <IconSymbol
                name="questionmark.circle.fill"
                size={20}
                color={Colors[colorScheme].tint}
              />
              <ThemedText style={{ color: Colors[colorScheme].tint }}>
                {showHint ? 'İpucunu Gizle' : 'İpucu Göster'}
              </ThemedText>
            </TouchableOpacity>

            {showHint && (
              <Animated.View
                entering={FadeInDown.duration(500)}
                style={styles.hintContent}
              >
                {currentStep.codeHint ? (
                  <CodeBlock code={currentStep.codeHint} />
                ) : (
                  <ThemedText>Bu adım için ipucu bulunmuyor.</ThemedText>
                )}
              </Animated.View>
            )}
          </View>
        </Animated.View>

        {/* Kod Editörü */}
        <ThemedView style={styles.editorContainer}>
          <ThemedView style={styles.editorHeader}>
            <ThemedText type="defaultSemiBold">Kod Editörü</ThemedText>
            <TouchableOpacity
              style={styles.previewButton}
              onPress={handleTogglePreview}
            >
              <IconSymbol
                name={showPreview ? 'pencil' : 'eye.fill'}
                size={20}
                color={Colors[colorScheme].tint}
              />
              <ThemedText style={{ color: Colors[colorScheme].tint }}>
                {showPreview ? 'Düzenle' : 'Önizleme'}
              </ThemedText>
            </TouchableOpacity>
          </ThemedView>

          {showPreview ? (
            <CodeBlock code={code} />
          ) : (
            <InteractiveCodeEditor
              code={code}
              onChangeCode={handleCodeChange}
            />
          )}
        </ThemedView>

        {/* Navigasyon Butonları */}
        <ThemedView style={styles.navigation}>
          <TouchableOpacity
            style={[
              styles.navButton,
              { opacity: currentStepIndex === 0 ? 0.5 : 1 }
            ]}
            onPress={handlePrevStep}
            disabled={currentStepIndex === 0}
          >
            <IconSymbol
              name="arrow.left"
              size={20}
              color={Colors[colorScheme].icon}
            />
            <ThemedText style={styles.navButtonText}>Önceki</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.nextButton,
              { backgroundColor: Colors[colorScheme].tint }
            ]}
            onPress={handleNextStep}
          >
            <ThemedText style={styles.buttonText}>
              {currentStepIndex < task.steps.length - 1 ? 'Sonraki Adım' : 'Tamamla'}
            </ThemedText>
            <IconSymbol
              name="arrow.right"
              size={20}
              color="white"
            />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
  },
  title: {
    marginBottom: 8,
  },
  description: {
    marginBottom: 16,
    opacity: 0.8,
  },
  metaContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  metaText: {
    marginLeft: 6,
    fontSize: 14,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  progressText: {
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  stepContainer: {
    padding: 20,
  },
  stepTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  hintContainer: {
    marginBottom: 16,
  },
  hintButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hintContent: {
    marginTop: 12,
  },
  editorContainer: {
    flex: 1,
    padding: 20,
    paddingTop: 0,
  },
  editorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  previewButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  navButtonText: {
    marginHorizontal: 8,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  button: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    margin: 20,
  },
  completedContainer: {
    padding: 20,
    alignItems: 'center',
  },
  completedIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  completedTitle: {
    marginBottom: 16,
  },
  completedMessage: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  completedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    width: '80%',
  },
  headerBackButton: {
    padding: 8,
    marginLeft: 8,
  },
});
