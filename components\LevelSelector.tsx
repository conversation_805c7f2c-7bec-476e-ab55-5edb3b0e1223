import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type LevelSelectorProps = {
  onSelectLevel: (level: string) => void;
  selectedLevel: string | null;
};

const levels = [
  {
    id: 'beginner',
    title: 'Başlangıç',
    description: 'React Native ile ilk kez tanışıyorum',
    icon: 'star.fill',
  },
  {
    id: 'intermediate',
    title: 'Orta',
    description: 'Temel bilgilere sahibim',
    icon: 'star.leadinghalf.filled',
  },
  {
    id: 'advanced',
    title: 'İleri',
    description: 'React Native ile projeler geliştirdim',
    icon: 'star.circle.fill',
  },
];

export function LevelSelector({ onSelectLevel, selectedLevel }: LevelSelectorProps) {
  const colorScheme = useColorScheme() ?? 'light';

  return (
    <ThemedView style={styles.container}>
      {levels.map((level) => (
        <TouchableOpacity
          key={level.id}
          style={[
            styles.levelCard,
            selectedLevel === level.id && {
              borderColor: Colors[colorScheme].tint,
              borderWidth: 2,
            },
          ]}
          onPress={() => onSelectLevel(level.id)}
        >
          <View style={styles.iconContainer}>
            <IconSymbol
              name={level.icon as any}
              size={28}
              color={Colors[colorScheme].tint}
            />
          </View>
          <View style={styles.textContainer}>
            <ThemedText type="defaultSemiBold">{level.title}</ThemedText>
            <ThemedText>{level.description}</ThemedText>
          </View>
        </TouchableOpacity>
      ))}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    gap: 10,
  },
  levelCard: {
    flexDirection: 'row',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 15,
  },
  textContainer: {
    flex: 1,
  },
});
