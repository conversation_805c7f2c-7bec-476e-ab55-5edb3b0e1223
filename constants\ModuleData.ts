// React Native öğrenme uygulaması için modül verileri
export type Module = {
  id: string;
  title: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // dakika cinsinden
  icon: string;
  lessons: Lesson[];
};

export type Lesson = {
  id: string;
  title: string;
  content: string;
  codeExample?: string;
  hasQuiz: boolean;
  hints?: string[]; // Kullanıcıya gösterilecek ipuçları
};

// Başlangıç seviyesi modülleri
export const beginnerModules: Module[] = [
  {
    id: 'intro-to-react-native',
    title: 'React Native\'e Giriş',
    description: 'React Native nedir, nasıl çalışır ve neden kullanılır?',
    level: 'beginner',
    duration: 15,
    icon: 'book.fill',
    lessons: [
      {
        id: 'what-is-react-native',
        title: 'React Native Nedir?',
        content: `# React Native Nedir?

React Native, Facebook tarafından geliştirilen açık kaynaklı bir mobil uygulama geliştirme framework'üdür. React Native ile iOS ve Android platformları için native mobil uygulamalar geliştirebilirsiniz.

## Neden React Native?

- **Tek Kod Tabanı**: Aynı kod tabanını kullanarak hem iOS hem de Android için uygulama geliştirebilirsiniz.
- **JavaScript**: Web geliştiricileri için tanıdık olan JavaScript dilini kullanır.
- **Hızlı Geliştirme**: Hot Reloading özelliği sayesinde değişiklikleri anında görebilirsiniz.
- **Native Performans**: JavaScript kodu, native bileşenlere dönüştürülür.
- **Geniş Topluluk**: Büyük bir geliştirici topluluğu ve hazır kütüphaneler.

## React Native vs Native Geliştirme

| React Native | Native Geliştirme |
|--------------|-------------------|
| Tek kod tabanı | Platform başına ayrı kod |
| JavaScript | Swift/Objective-C (iOS), Java/Kotlin (Android) |
| Hızlı geliştirme | Daha uzun geliştirme süreci |
| Orta-yüksek performans | En yüksek performans |
| Kolay öğrenme eğrisi | Daha dik öğrenme eğrisi |`,
        hasQuiz: true,
        hints: [
          "React Native, React'in mobil uygulama geliştirme için özelleştirilmiş bir versiyonudur.",
          "React Native ile yazılan kod, native bileşenlere dönüştürülür, bu da web view'lar yerine gerçek native performans sağlar.",
          "React Native, 'Learn once, write anywhere' (Bir kez öğren, her yerde yaz) felsefesini benimser.",
          "React Native ile geliştirilen uygulamalar, App Store ve Google Play Store'da yayınlanabilir.",
          "React Native, Facebook, Instagram, Discord, Skype gibi popüler uygulamalar tarafından kullanılmaktadır."
        ]
      },
      {
        id: 'setting-up-environment',
        title: 'Geliştirme Ortamının Kurulumu',
        content: `# Geliştirme Ortamının Kurulumu

React Native uygulaması geliştirmek için iki yol vardır:
1. **Expo CLI**: Başlangıç için daha kolay, hızlı bir şekilde başlamanızı sağlar.
2. **React Native CLI**: Daha fazla özelleştirme imkanı sunar, native modüller ekleyebilirsiniz.

Bu eğitimde Expo CLI kullanacağız çünkü başlangıç için daha uygundur.

## Expo CLI Kurulumu

1. Node.js'i yükleyin (https://nodejs.org)
2. Expo CLI'ı yükleyin:
\`\`\`bash
npm install -g expo-cli
\`\`\`

## İlk Projenizi Oluşturun

\`\`\`bash
expo init MyFirstApp
cd MyFirstApp
npm start
\`\`\`

Bu komutlar:
1. "MyFirstApp" adında yeni bir proje oluşturur
2. Proje klasörüne girer
3. Geliştirme sunucusunu başlatır

## Expo Go Uygulaması

Telefonunuza Expo Go uygulamasını yükleyerek, bilgisayarınızda geliştirdiğiniz uygulamayı anında telefonunuzda test edebilirsiniz.

- iOS: App Store'dan "Expo Go" uygulamasını indirin
- Android: Google Play'den "Expo Go" uygulamasını indirin`,
        hasQuiz: false
      }
    ]
  },
  {
    id: 'jsx-basics',
    title: 'JSX Temelleri',
    description: 'JSX sözdizimi ve React Native bileşenleri',
    level: 'beginner',
    duration: 20,
    icon: 'chevron.left.forwardslash.chevron.right',
    lessons: [
      {
        id: 'understanding-jsx',
        title: 'JSX Nedir?',
        content: `# JSX Nedir?

JSX (JavaScript XML), JavaScript içinde HTML benzeri kod yazmanıza olanak tanıyan bir sözdizimi uzantısıdır. React ve React Native'de kullanıcı arayüzü oluşturmak için kullanılır.

## JSX Örneği

\`\`\`jsx
const element = <Text>Merhaba Dünya!</Text>;
\`\`\`

Bu kod, JavaScript içinde bir XML benzeri yapı tanımlar. JSX, React Native'de UI bileşenlerini tanımlamanın sezgisel bir yolunu sunar.

## JSX Kuralları

1. Her JSX ifadesi tek bir kök element içermelidir
2. Tüm JSX etiketleri kapatılmalıdır (self-closing veya açma/kapama etiketleri)
3. JSX içinde JavaScript ifadeleri kullanmak için süslü parantezler kullanılır: \`{expression}\`
4. JSX içindeki özellikler camelCase olarak yazılır (örn. \`backgroundColor\` HTML'deki \`background-color\` yerine)

## JSX Örneği

\`\`\`jsx
function Greeting(props) {
  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 24 }}>Merhaba, {props.name}!</Text>
    </View>
  );
}
\`\`\`

Bu örnekte:
- \`<View>\` ve \`<Text>\` React Native bileşenleridir
- \`style\` özellikleri JavaScript nesneleri olarak tanımlanır
- \`{props.name}\` bir JavaScript ifadesidir ve dinamik içerik ekler`,
        codeExample: `import React from 'react';
import { View, Text } from 'react-native';

export default function App() {
  const name = "React Native";

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: 24 }}>Merhaba, {name}!</Text>
    </View>
  );
}`,
        hasQuiz: true,
        hints: [
          "JSX, JavaScript XML'in kısaltmasıdır ve JavaScript içinde HTML benzeri kod yazmanıza olanak tanır.",
          "JSX, React ve React Native'de UI bileşenlerini tanımlamanın daha sezgisel bir yolunu sunar.",
          "JSX ifadeleri, JavaScript fonksiyonları içinde return edilebilir ve değişkenlere atanabilir.",
          "JSX içinde JavaScript ifadeleri kullanmak için süslü parantezler {} kullanılır.",
          "JSX, transpile edildiğinde React.createElement() fonksiyon çağrılarına dönüştürülür."
        ]
      }
    ]
  }
];

// Orta seviye modülleri
export const intermediateModules: Module[] = [
  {
    id: 'components-and-props',
    title: 'Bileşenler ve Props',
    description: 'React Native bileşenleri ve props kavramı',
    level: 'intermediate',
    duration: 25,
    icon: 'doc.text.fill',
    lessons: [
      {
        id: 'functional-components',
        title: 'Fonksiyonel Bileşenler',
        content: `# Fonksiyonel Bileşenler

React Native'de fonksiyonel bileşenler, UI'ın yeniden kullanılabilir parçalarını oluşturmak için kullanılan JavaScript fonksiyonlarıdır.

## Basit Bir Fonksiyonel Bileşen

\`\`\`jsx
import React from 'react';
import { Text, View } from 'react-native';

function Greeting() {
  return (
    <View>
      <Text>Merhaba!</Text>
    </View>
  );
}

export default Greeting;
\`\`\`

## Props ile Bileşen Kullanımı

Props (properties), bileşenlere veri aktarmanın bir yoludur. Bir bileşeni çağırırken özellikler ekleyebilir ve bileşen içinde bu özelliklere erişebilirsiniz.

\`\`\`jsx
import React from 'react';
import { Text, View } from 'react-native';

// Props alan bir bileşen
function Greeting(props) {
  return (
    <View>
      <Text>Merhaba, {props.name}!</Text>
    </View>
  );
}

// Ana bileşen
export default function App() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Greeting name="Ahmet" />
      <Greeting name="Ayşe" />
    </View>
  );
}
\`\`\`

## Destructuring ile Props

Modern JavaScript'te, props'ları daha temiz bir şekilde kullanmak için destructuring kullanabilirsiniz:

\`\`\`jsx
function Greeting({ name, age }) {
  return (
    <View>
      <Text>Merhaba, {name}! Yaşınız: {age}</Text>
    </View>
  );
}

// Kullanımı
<Greeting name="Ahmet" age={25} />
\`\`\``,
        codeExample: `import React from 'react';
import { View, Text } from 'react-native';

// Props alan bir bileşen
function UserCard({ name, age, job }) {
  return (
    <View style={{
      padding: 20,
      margin: 10,
      backgroundColor: '#f0f0f0',
      borderRadius: 10
    }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold' }}>{name}</Text>
      <Text>Yaş: {age}</Text>
      <Text>Meslek: {job}</Text>
    </View>
  );
}

export default function App() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', padding: 20 }}>
      <UserCard name="Ahmet Yılmaz" age={28} job="Yazılım Geliştirici" />
      <UserCard name="Ayşe Demir" age={34} job="Grafik Tasarımcı" />
    </View>
  );
}`,
        hasQuiz: true,
        hints: [
          "Fonksiyonel bileşenler, React ve React Native'de UI'ın yeniden kullanılabilir parçalarını oluşturmak için kullanılan JavaScript fonksiyonlarıdır.",
          "Props, bileşenlere veri aktarmanın bir yoludur ve bileşenin davranışını veya görünümünü özelleştirmek için kullanılır.",
          "Destructuring, props'ları daha temiz bir şekilde kullanmanın modern bir yoludur: function MyComponent({ name, age }) { ... }",
          "Fonksiyonel bileşenler, React Hooks ile birlikte state ve yaşam döngüsü özelliklerini de kullanabilir.",
          "Bileşenler, uygulamanızı daha modüler ve bakımı kolay hale getirir."
        ]
      }
    ]
  }
];

// İleri seviye modülleri
export const advancedModules: Module[] = [
  {
    id: 'navigation',
    title: 'React Navigation',
    description: 'React Native uygulamalarında ekranlar arası geçiş',
    level: 'advanced',
    duration: 30,
    icon: 'arrow.right',
    lessons: [
      {
        id: 'stack-navigation',
        title: 'Stack Navigation',
        content: `# Stack Navigation

React Navigation, React Native uygulamalarında ekranlar arası geçiş için en popüler çözümdür. Stack Navigator, ekranları bir yığın (stack) olarak yönetir - yeni bir ekrana gittiğinizde, o ekran yığının üstüne eklenir.

## Kurulum

\`\`\`bash
npm install @react-navigation/native
npm install @react-navigation/stack
expo install react-native-screens react-native-safe-area-context
\`\`\`

## Temel Kullanım

\`\`\`jsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, Button } from 'react-native';

// Ana ekran bileşeni
function HomeScreen({ navigation }) {
  return (
    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
      <Text>Ana Ekran</Text>
      <Button
        title="Detay Ekranına Git"
        onPress={() => navigation.navigate('Details', { itemId: 86 })}
      />
    </View>
  );
}

// Detay ekranı bileşeni
function DetailsScreen({ route, navigation }) {
  // route.params ile gönderilen parametrelere erişim
  const { itemId } = route.params;

  return (
    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
      <Text>Detay Ekranı</Text>
      <Text>Item ID: {itemId}</Text>
      <Button
        title="Geri Dön"
        onPress={() => navigation.goBack()}
      />
    </View>
  );
}

const Stack = createStackNavigator();

function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{ title: 'Ana Sayfa' }}
        />
        <Stack.Screen
          name="Details"
          component={DetailsScreen}
          options={{ title: 'Detaylar' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default App;
\`\`\`

## Navigasyon İşlemleri

- **navigate**: Belirtilen ekrana gider, eğer zaten yığında varsa o ekranı öne getirir
- **push**: Aynı ekranın birden fazla örneğini yığına ekleyebilir
- **goBack**: Bir önceki ekrana döner
- **popToTop**: Yığındaki ilk ekrana döner (ana ekran)
- **replace**: Mevcut ekranı yeni bir ekranla değiştirir`,
        codeExample: `import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, Button, StyleSheet } from 'react-native';

// Ana ekran bileşeni
function HomeScreen({ navigation }) {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Hoş Geldiniz!</Text>
      <Button
        title="Profil Ekranına Git"
        onPress={() => navigation.navigate('Profile', { userId: 123, username: 'reactnative_dev' })}
      />
    </View>
  );
}

// Profil ekranı bileşeni
function ProfileScreen({ route, navigation }) {
  const { userId, username } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Profil Ekranı</Text>
      <Text style={styles.info}>Kullanıcı ID: {userId}</Text>
      <Text style={styles.info}>Kullanıcı Adı: {username}</Text>
      <View style={styles.buttonContainer}>
        <Button
          title="Ayarlar"
          onPress={() => navigation.navigate('Settings')}
        />
        <Button
          title="Ana Sayfaya Dön"
          onPress={() => navigation.popToTop()}
          color="#666"
        />
      </View>
    </View>
  );
}

// Ayarlar ekranı bileşeni
function SettingsScreen({ navigation }) {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ayarlar</Text>
      <Button
        title="Geri Dön"
        onPress={() => navigation.goBack()}
      />
    </View>
  );
}

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#61DAFB',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{ title: 'Ana Sayfa' }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={({ route }) => ({ title: route.params.username })}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{ title: 'Ayarlar' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  info: {
    fontSize: 16,
    marginBottom: 10,
  },
  buttonContainer: {
    marginTop: 20,
    width: '100%',
    justifyContent: 'space-around',
    flexDirection: 'row',
  }
});`,
        hasQuiz: true
      }
    ]
  }
];

// Tüm modülleri birleştir
export const allModules: Module[] = [
  ...beginnerModules,
  ...intermediateModules,
  ...advancedModules
];

// Modül ID'sine göre modül getir
export function getModuleById(id: string): Module | undefined {
  return allModules.find(module => module.id === id);
}

// Seviyeye göre modülleri getir
export function getModulesByLevel(level: 'beginner' | 'intermediate' | 'advanced'): Module[] {
  return allModules.filter(module => module.level === level);
}
