import React from 'react';
import { StyleSheet, Image, View, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function WelcomeScreen() {
  const colorScheme = useColorScheme() ?? 'light';

  const handleRegister = () => {
    router.push('/auth/register');
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  return (
    <ThemedView style={styles.container}>
      <Animated.View entering={FadeInUp.delay(300).duration(1000)} style={styles.logoContainer}>
        <Image
          source={require('@/assets/images/react-logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>

      <Animated.View entering={FadeInDown.delay(600).duration(1000)} style={styles.contentContainer}>
        <ThemedText type="title" style={styles.title}>
          Reactigo'ya Hoş Geldiniz!
        </ThemedText>

        <ThemedText style={styles.subtitle}>
          Bu uygulama ile React Native'i sıfırdan öğrenebilir, interaktif örneklerle pratik yapabilirsiniz.
        </ThemedText>

        <TouchableOpacity
          style={[
            styles.registerButton,
            { backgroundColor: Colors[colorScheme].tint }
          ]}
          onPress={handleRegister}
        >
          <ThemedText style={styles.buttonText}>Kayıt Ol</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.loginButton,
            { borderColor: Colors[colorScheme].tint }
          ]}
          onPress={handleLogin}
        >
          <ThemedText style={[styles.loginButtonText, { color: Colors[colorScheme].tint }]}>
            Giriş Yap
          </ThemedText>
        </TouchableOpacity>
      </Animated.View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 20,
  },
  logo: {
    width: 150,
    height: 150,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },
  title: {
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 40,
  },
  registerButton: {
    width: '100%',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 15,
  },
  loginButton: {
    width: '100%',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  loginButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
});
