import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View, Image } from 'react-native';
import { router } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ProgressBar } from '@/components/ProgressBar';

// Örnek kullanıcı verileri
const userData = {
  name: '<PERSON><PERSON> Yılmaz',
  email: '<EMAIL>',
  level: 'beginner',
  completedModules: 3,
  totalModules: 12,
  completedQuizzes: 5,
  totalQuizzes: 20,
  completedPractices: 2,
  totalPractices: 8,
  badges: [
    { id: 'first-module', name: '<PERSON><PERSON>dül', icon: 'star.fill', color: '#4CAF50' },
    { id: 'first-quiz', name: '<PERSON>lk Quiz', icon: 'checkmark.circle.fill', color: '#2196F3' },
    { id: 'first-practice', name: 'İlk Uygulama', icon: 'hammer.fill', color: '#FF9800' },
  ]
};

export default function ProfileScreen() {
  const colorScheme = useColorScheme() ?? 'light';

  const handleLogout = () => {
    // Çıkış işlemi
    router.replace('/');
  };

  return (
    <ScrollView style={styles.scrollView}>
      <ThemedView style={styles.container}>
        {/* Profil Başlığı */}
        <ThemedView style={styles.header}>
          <View style={styles.profileImageContainer}>
            <View style={[styles.profileImage, { backgroundColor: Colors[colorScheme].tint }]}>
              <ThemedText style={styles.profileInitial}>
                {userData.name.charAt(0)}
              </ThemedText>
            </View>
          </View>
          
          <ThemedText type="title" style={styles.name}>
            {userData.name}
          </ThemedText>
          
          <ThemedText style={styles.email}>
            {userData.email}
          </ThemedText>
          
          <ThemedView style={styles.levelContainer}>
            <IconSymbol
              name="star.fill"
              size={16}
              color={Colors[colorScheme].tint}
            />
            <ThemedText style={styles.levelText}>
              {userData.level === 'beginner' ? 'Başlangıç' : 
               userData.level === 'intermediate' ? 'Orta' : 'İleri'} Seviye
            </ThemedText>
          </ThemedView>
        </ThemedView>
        
        {/* İlerleme İstatistikleri */}
        <ThemedView style={styles.statsContainer}>
          <ThemedText type="subtitle">İlerleme Durumu</ThemedText>
          
          <ThemedView style={styles.statItem}>
            <ThemedView style={styles.statLabelContainer}>
              <ThemedText type="defaultSemiBold">Modüller</ThemedText>
              <ThemedText>
                {userData.completedModules}/{userData.totalModules}
              </ThemedText>
            </ThemedView>
            <ProgressBar progress={userData.completedModules / userData.totalModules} />
          </ThemedView>
          
          <ThemedView style={styles.statItem}>
            <ThemedView style={styles.statLabelContainer}>
              <ThemedText type="defaultSemiBold">Quiz'ler</ThemedText>
              <ThemedText>
                {userData.completedQuizzes}/{userData.totalQuizzes}
              </ThemedText>
            </ThemedView>
            <ProgressBar progress={userData.completedQuizzes / userData.totalQuizzes} />
          </ThemedView>
          
          <ThemedView style={styles.statItem}>
            <ThemedView style={styles.statLabelContainer}>
              <ThemedText type="defaultSemiBold">Uygulamalar</ThemedText>
              <ThemedText>
                {userData.completedPractices}/{userData.totalPractices}
              </ThemedText>
            </ThemedView>
            <ProgressBar progress={userData.completedPractices / userData.totalPractices} />
          </ThemedView>
        </ThemedView>
        
        {/* Rozetler */}
        <ThemedView style={styles.badgesContainer}>
          <ThemedText type="subtitle">Kazanılan Rozetler</ThemedText>
          
          <ThemedView style={styles.badgesList}>
            {userData.badges.map((badge, index) => (
              <Animated.View 
                key={badge.id}
                entering={FadeIn.delay(index * 200).duration(500)}
                style={styles.badgeItem}
              >
                <View style={[styles.badgeIcon, { backgroundColor: badge.color }]}>
                  <IconSymbol
                    name={badge.icon as any}
                    size={24}
                    color="white"
                  />
                </View>
                <ThemedText style={styles.badgeName}>{badge.name}</ThemedText>
              </Animated.View>
            ))}
          </ThemedView>
        </ThemedView>
        
        {/* Ayarlar */}
        <ThemedView style={styles.settingsContainer}>
          <ThemedText type="subtitle">Ayarlar</ThemedText>
          
          <TouchableOpacity style={styles.settingItem}>
            <IconSymbol
              name="gear"
              size={20}
              color={Colors[colorScheme].icon}
            />
            <ThemedText style={styles.settingText}>Profil Ayarları</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <IconSymbol
              name="bell.fill"
              size={20}
              color={Colors[colorScheme].icon}
            />
            <ThemedText style={styles.settingText}>Bildirim Ayarları</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem} onPress={handleLogout}>
            <IconSymbol
              name="arrow.left"
              size={20}
              color={Colors[colorScheme].error}
            />
            <ThemedText style={[styles.settingText, { color: Colors[colorScheme].error }]}>
              Çıkış Yap
            </ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 30,
  },
  profileImageContainer: {
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitial: {
    fontSize: 40,
    fontWeight: 'bold',
    color: 'white',
  },
  name: {
    marginBottom: 4,
  },
  email: {
    marginBottom: 12,
    opacity: 0.7,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(97, 218, 251, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  levelText: {
    marginLeft: 6,
    fontSize: 14,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statItem: {
    marginTop: 16,
  },
  statLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  badgesContainer: {
    marginBottom: 24,
  },
  badgesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  badgeItem: {
    alignItems: 'center',
    width: '30%',
    marginBottom: 20,
  },
  badgeIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  badgeName: {
    textAlign: 'center',
    fontSize: 12,
  },
  settingsContainer: {
    marginBottom: 40,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  settingText: {
    marginLeft: 12,
    fontSize: 16,
  },
});
